package com.checklist.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

/**
 * 版本比较结果
 */
public class VersionComparison {
    @JsonProperty("templateId")
    private String templateId;
    
    @JsonProperty("currentVersion")
    private String currentVersion;
    
    @JsonProperty("targetVersion")
    private String targetVersion;
    
    @JsonProperty("changes")
    private VersionChanges changes;
    
    @JsonProperty("summary")
    private ChangeSummary summary;
    
    // Getters and Setters
    public String getTemplateId() { return templateId; }
    public void setTemplateId(String templateId) { this.templateId = templateId; }
    
    public String getCurrentVersion() { return currentVersion; }
    public void setCurrentVersion(String currentVersion) { this.currentVersion = currentVersion; }
    
    public String getTargetVersion() { return targetVersion; }
    public void setTargetVersion(String targetVersion) { this.targetVersion = targetVersion; }
    
    public VersionChanges getChanges() { return changes; }
    public void setChanges(VersionChanges changes) { this.changes = changes; }
    
    public ChangeSummary getSummary() { return summary; }
    public void setSummary(ChangeSummary summary) { this.summary = summary; }
    
    public static class VersionChanges {
        @JsonProperty("template")
        private List<Map<String, Object>> template;
        
        @JsonProperty("items")
        private List<Map<String, Object>> items;
        
        @JsonProperty("config")
        private List<Map<String, Object>> config;
        
        // Getters and Setters
        public List<Map<String, Object>> getTemplate() { return template; }
        public void setTemplate(List<Map<String, Object>> template) { this.template = template; }
        
        public List<Map<String, Object>> getItems() { return items; }
        public void setItems(List<Map<String, Object>> items) { this.items = items; }
        
        public List<Map<String, Object>> getConfig() { return config; }
        public void setConfig(List<Map<String, Object>> config) { this.config = config; }
    }
    
    public static class ChangeSummary {
        @JsonProperty("totalChanges")
        private int totalChanges;
        
        @JsonProperty("addedItems")
        private int addedItems;
        
        @JsonProperty("removedItems")
        private int removedItems;
        
        @JsonProperty("modifiedItems")
        private int modifiedItems;
        
        @JsonProperty("configChanges")
        private int configChanges;
        
        // Getters and Setters
        public int getTotalChanges() { return totalChanges; }
        public void setTotalChanges(int totalChanges) { this.totalChanges = totalChanges; }
        
        public int getAddedItems() { return addedItems; }
        public void setAddedItems(int addedItems) { this.addedItems = addedItems; }
        
        public int getRemovedItems() { return removedItems; }
        public void setRemovedItems(int removedItems) { this.removedItems = removedItems; }
        
        public int getModifiedItems() { return modifiedItems; }
        public void setModifiedItems(int modifiedItems) { this.modifiedItems = modifiedItems; }
        
        public int getConfigChanges() { return configChanges; }
        public void setConfigChanges(int configChanges) { this.configChanges = configChanges; }
    }
}
