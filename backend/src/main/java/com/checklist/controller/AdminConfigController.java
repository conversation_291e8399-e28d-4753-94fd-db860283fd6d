package com.checklist.controller;

import com.checklist.service.AdminConfigService;
import com.checklist.common.ApiResponse;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 管理配置控制器
 */
@RestController
@RequestMapping("/api/admin/config")
@CrossOrigin(origins = "*")
public class AdminConfigController {
    
    @Autowired
    private AdminConfigService adminConfigService;
    
    /**
     * 获取模板的状态按钮组配置
     */
    @GetMapping("/templates/{templateId}/button-groups")
    public ApiResponse<List<Map<String, Object>>> getTemplateButtonGroups(@PathVariable String templateId) {
        try {
            List<Map<String, Object>> buttonGroups = adminConfigService.getTemplateButtonGroups(templateId);
            return ApiResponse.success(buttonGroups);
            
        } catch (Exception e) {
            return ApiResponse.error("获取模板按钮组配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存模板的状态按钮组配置
     */
    @PutMapping("/templates/{templateId}/button-groups")
    public ApiResponse<String> saveTemplateButtonGroups(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> buttonGroups = (List<Map<String, Object>>) request.get("buttonGroups");
            
            if (buttonGroups == null) {
                return ApiResponse.error("按钮组配置不能为空");
            }
            
            // 验证配置
            String validationError = validateButtonGroups(buttonGroups);
            if (validationError != null) {
                return ApiResponse.error("按钮组配置验证失败: " + validationError);
            }
            
            adminConfigService.saveTemplateButtonGroups(templateId, buttonGroups);
            return ApiResponse.success("按钮组配置保存成功");
            
        } catch (Exception e) {
            return ApiResponse.error("保存模板按钮组配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取模板的缺陷规则配置
     */
    @GetMapping("/templates/{templateId}/defect-rules")
    public ApiResponse<List<Map<String, Object>>> getTemplateDefectRules(@PathVariable String templateId) {
        try {
            List<Map<String, Object>> defectRules = adminConfigService.getTemplateDefectRules(templateId);
            return ApiResponse.success(defectRules);
            
        } catch (Exception e) {
            return ApiResponse.error("获取模板缺陷规则配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存模板的缺陷规则配置
     */
    @PutMapping("/templates/{templateId}/defect-rules")
    public ApiResponse<String> saveTemplateDefectRules(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> defectRules = (List<Map<String, Object>>) request.get("defectRules");
            
            if (defectRules == null) {
                return ApiResponse.error("缺陷规则配置不能为空");
            }
            
            // 验证配置
            String validationError = validateDefectRules(defectRules);
            if (validationError != null) {
                return ApiResponse.error("缺陷规则配置验证失败: " + validationError);
            }
            
            adminConfigService.saveTemplateDefectRules(templateId, defectRules);
            return ApiResponse.success("缺陷规则配置保存成功");
            
        } catch (Exception e) {
            return ApiResponse.error("保存模板缺陷规则配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取模板的完整配置
     */
    @GetMapping("/templates/{templateId}/config")
    public ApiResponse<Map<String, Object>> getTemplateConfig(@PathVariable String templateId) {
        try {
            Map<String, Object> config = adminConfigService.getTemplateConfig(templateId);
            return ApiResponse.success(config);
            
        } catch (Exception e) {
            return ApiResponse.error("获取模板配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存模板的完整配置
     */
    @PutMapping("/templates/{templateId}/config")
    public ApiResponse<String> saveTemplateConfig(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> config) {
        try {
            // 验证按钮组配置
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> buttonGroups = (List<Map<String, Object>>) config.get("buttonGroups");
            if (buttonGroups != null) {
                String validationError = validateButtonGroups(buttonGroups);
                if (validationError != null) {
                    return ApiResponse.error("按钮组配置验证失败: " + validationError);
                }
            }
            
            // 验证缺陷规则配置
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> defectRules = (List<Map<String, Object>>) config.get("defectRules");
            if (defectRules != null) {
                String validationError = validateDefectRules(defectRules);
                if (validationError != null) {
                    return ApiResponse.error("缺陷规则配置验证失败: " + validationError);
                }
            }
            
            adminConfigService.saveTemplateConfig(templateId, config);
            return ApiResponse.success("模板配置保存成功");
            
        } catch (Exception e) {
            return ApiResponse.error("保存模板配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建状态按钮组
     */
    @PostMapping("/button-groups")
    public ApiResponse<Map<String, Object>> createButtonGroup(@RequestBody Map<String, Object> buttonGroup) {
        try {
            // 验证数据
            String validationError = validateButtonGroup(buttonGroup);
            if (validationError != null) {
                return ApiResponse.error("按钮组配置验证失败: " + validationError);
            }
            
            Map<String, Object> createdGroup = adminConfigService.createButtonGroup(buttonGroup);
            return ApiResponse.success(createdGroup);
            
        } catch (Exception e) {
            return ApiResponse.error("创建按钮组失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新状态按钮组
     */
    @PutMapping("/button-groups/{groupId}")
    public ApiResponse<Map<String, Object>> updateButtonGroup(
            @PathVariable String groupId, 
            @RequestBody Map<String, Object> buttonGroup) {
        try {
            // 验证数据
            String validationError = validateButtonGroup(buttonGroup);
            if (validationError != null) {
                return ApiResponse.error("按钮组配置验证失败: " + validationError);
            }
            
            Map<String, Object> updatedGroup = adminConfigService.updateButtonGroup(groupId, buttonGroup);
            if (updatedGroup == null) {
                return ApiResponse.error("按钮组不存在");
            }
            
            return ApiResponse.success(updatedGroup);
            
        } catch (Exception e) {
            return ApiResponse.error("更新按钮组失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除状态按钮组
     */
    @DeleteMapping("/button-groups/{groupId}")
    public ApiResponse<String> deleteButtonGroup(@PathVariable String groupId) {
        try {
            boolean success = adminConfigService.deleteButtonGroup(groupId);
            if (!success) {
                return ApiResponse.error("按钮组不存在");
            }
            
            return ApiResponse.success("按钮组删除成功");
            
        } catch (Exception e) {
            return ApiResponse.error("删除按钮组失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建缺陷规则
     */
    @PostMapping("/defect-rules")
    public ApiResponse<Map<String, Object>> createDefectRule(@RequestBody Map<String, Object> defectRule) {
        try {
            // 验证数据
            String validationError = validateDefectRule(defectRule);
            if (validationError != null) {
                return ApiResponse.error("缺陷规则配置验证失败: " + validationError);
            }
            
            Map<String, Object> createdRule = adminConfigService.createDefectRule(defectRule);
            return ApiResponse.success(createdRule);
            
        } catch (Exception e) {
            return ApiResponse.error("创建缺陷规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新缺陷规则
     */
    @PutMapping("/defect-rules/{ruleId}")
    public ApiResponse<Map<String, Object>> updateDefectRule(
            @PathVariable String ruleId, 
            @RequestBody Map<String, Object> defectRule) {
        try {
            // 验证数据
            String validationError = validateDefectRule(defectRule);
            if (validationError != null) {
                return ApiResponse.error("缺陷规则配置验证失败: " + validationError);
            }
            
            Map<String, Object> updatedRule = adminConfigService.updateDefectRule(ruleId, defectRule);
            if (updatedRule == null) {
                return ApiResponse.error("缺陷规则不存在");
            }
            
            return ApiResponse.success(updatedRule);
            
        } catch (Exception e) {
            return ApiResponse.error("更新缺陷规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除缺陷规则
     */
    @DeleteMapping("/defect-rules/{ruleId}")
    public ApiResponse<String> deleteDefectRule(@PathVariable String ruleId) {
        try {
            boolean success = adminConfigService.deleteDefectRule(ruleId);
            if (!success) {
                return ApiResponse.error("缺陷规则不存在");
            }
            
            return ApiResponse.success("缺陷规则删除成功");
            
        } catch (Exception e) {
            return ApiResponse.error("删除缺陷规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试缺陷规则
     */
    @PostMapping("/defect-rules/{ruleId}/test")
    public ApiResponse<Map<String, Object>> testDefectRule(
            @PathVariable String ruleId, 
            @RequestBody Map<String, Object> testData) {
        try {
            Map<String, Object> result = adminConfigService.testDefectRule(ruleId, testData);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("测试缺陷规则失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有可用的API接口列表
     */
    @GetMapping("/api-endpoints")
    public ApiResponse<List<Map<String, Object>>> getAvailableApiEndpoints() {
        try {
            List<Map<String, Object>> endpoints = adminConfigService.getAvailableApiEndpoints();
            return ApiResponse.success(endpoints);
            
        } catch (Exception e) {
            return ApiResponse.error("获取API接口列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证模板配置
     */
    @PostMapping("/templates/{templateId}/validate")
    public ApiResponse<Map<String, Object>> validateTemplateConfig(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> config) {
        try {
            Map<String, Object> result = adminConfigService.validateTemplateConfig(templateId, config);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("验证模板配置失败: " + e.getMessage());
        }
    }
    
    // 私有验证方法
    
    private String validateButtonGroups(List<Map<String, Object>> buttonGroups) {
        for (int i = 0; i < buttonGroups.size(); i++) {
            String error = validateButtonGroup(buttonGroups.get(i));
            if (error != null) {
                return "按钮组 " + (i + 1) + ": " + error;
            }
        }
        return null;
    }
    
    private String validateButtonGroup(Map<String, Object> buttonGroup) {
        String name = (String) buttonGroup.get("name");
        if (name == null || name.trim().isEmpty()) {
            return "按钮组名称不能为空";
        }
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> buttons = (List<Map<String, Object>>) buttonGroup.get("buttons");
        if (buttons == null || buttons.isEmpty()) {
            return "按钮组至少需要包含一个按钮";
        }
        
        for (int i = 0; i < buttons.size(); i++) {
            Map<String, Object> button = buttons.get(i);
            String label = (String) button.get("label");
            if (label == null || label.trim().isEmpty()) {
                return "第" + (i + 1) + "个按钮的标签不能为空";
            }
            
            String status = (String) button.get("status");
            if (status == null || status.trim().isEmpty()) {
                return "第" + (i + 1) + "个按钮的状态值不能为空";
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> action = (Map<String, Object>) button.get("action");
            if (action != null) {
                String apiEndpoint = (String) action.get("apiEndpoint");
                if (apiEndpoint == null || apiEndpoint.trim().isEmpty()) {
                    return "第" + (i + 1) + "个按钮的API接口不能为空";
                }
            }
        }
        
        return null;
    }
    
    private String validateDefectRules(List<Map<String, Object>> defectRules) {
        for (int i = 0; i < defectRules.size(); i++) {
            String error = validateDefectRule(defectRules.get(i));
            if (error != null) {
                return "规则 " + (i + 1) + ": " + error;
            }
        }
        return null;
    }
    
    private String validateDefectRule(Map<String, Object> defectRule) {
        String name = (String) defectRule.get("name");
        if (name == null || name.trim().isEmpty()) {
            return "规则名称不能为空";
        }
        
        @SuppressWarnings("unchecked")
        Map<String, Object> trigger = (Map<String, Object>) defectRule.get("trigger");
        if (trigger != null) {
            @SuppressWarnings("unchecked")
            List<String> status = (List<String>) trigger.get("status");
            if (status == null || status.isEmpty()) {
                return "触发状态不能为空";
            }
        }
        
        @SuppressWarnings("unchecked")
        Map<String, Object> template = (Map<String, Object>) defectRule.get("template");
        if (template != null) {
            String titleTemplate = (String) template.get("titleTemplate");
            if (titleTemplate == null || titleTemplate.trim().isEmpty()) {
                return "标题模板不能为空";
            }
            
            String descriptionTemplate = (String) template.get("descriptionTemplate");
            if (descriptionTemplate == null || descriptionTemplate.trim().isEmpty()) {
                return "描述模板不能为空";
            }
        }
        
        return null;
    }
}
