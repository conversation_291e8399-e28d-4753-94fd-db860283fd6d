package com.checklist.controller;

import com.checklist.model.TemplateSnapshot;
import com.checklist.model.ReviewTemplateVersion;
import com.checklist.model.VersionComparison;
import com.checklist.service.TemplateVersionService;
import com.checklist.common.ApiResponse;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 模板版本管理控制器
 */
@RestController
@RequestMapping("/api/template-version")
@CrossOrigin(origins = "*")
public class TemplateVersionController {
    
    @Autowired
    private TemplateVersionService templateVersionService;
    
    /**
     * 创建模板快照
     */
    @PostMapping("/snapshots")
    public ApiResponse<TemplateSnapshot> createSnapshot(@RequestBody Map<String, Object> request) {
        try {
            String templateId = (String) request.get("templateId");
            String version = (String) request.get("version");
            String description = (String) request.get("description");
            
            if (templateId == null || templateId.trim().isEmpty()) {
                return ApiResponse.error("模板ID不能为空");
            }
            if (version == null || version.trim().isEmpty()) {
                return ApiResponse.error("版本号不能为空");
            }
            
            TemplateSnapshot snapshot = templateVersionService.createSnapshot(templateId, version, description);
            return ApiResponse.success(snapshot);
            
        } catch (Exception e) {
            return ApiResponse.error("创建模板快照失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取模板的所有快照
     */
    @GetMapping("/templates/{templateId}/snapshots")
    public ApiResponse<Map<String, Object>> getTemplateSnapshots(
            @PathVariable String templateId,
            @RequestParam(defaultValue = "20") int limit,
            @RequestParam(defaultValue = "0") int offset,
            @RequestParam(defaultValue = "false") boolean includeContent) {
        try {
            Map<String, Object> result = templateVersionService.getTemplateSnapshots(
                templateId, limit, offset, includeContent);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("获取模板快照失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取特定快照详情
     */
    @GetMapping("/snapshots/{snapshotId}")
    public ApiResponse<TemplateSnapshot> getSnapshot(@PathVariable String snapshotId) {
        try {
            TemplateSnapshot snapshot = templateVersionService.getSnapshot(snapshotId);
            if (snapshot == null) {
                return ApiResponse.error("快照不存在");
            }
            return ApiResponse.success(snapshot);
            
        } catch (Exception e) {
            return ApiResponse.error("获取快照详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除快照
     */
    @DeleteMapping("/snapshots/{snapshotId}")
    public ApiResponse<String> deleteSnapshot(@PathVariable String snapshotId) {
        try {
            boolean success = templateVersionService.deleteSnapshot(snapshotId);
            if (!success) {
                return ApiResponse.error("快照不存在");
            }
            return ApiResponse.success("快照删除成功");
            
        } catch (Exception e) {
            return ApiResponse.error("删除快照失败: " + e.getMessage());
        }
    }
    
    /**
     * 为检查单创建模板版本关联
     */
    @PostMapping("/review-versions")
    public ApiResponse<ReviewTemplateVersion> createReviewTemplateVersion(@RequestBody Map<String, Object> request) {
        try {
            String reviewId = (String) request.get("reviewId");
            String templateId = (String) request.get("templateId");
            String snapshotId = (String) request.get("snapshotId");
            
            if (reviewId == null || reviewId.trim().isEmpty()) {
                return ApiResponse.error("检查单ID不能为空");
            }
            if (templateId == null || templateId.trim().isEmpty()) {
                return ApiResponse.error("模板ID不能为空");
            }
            
            ReviewTemplateVersion reviewVersion = templateVersionService.createReviewTemplateVersion(
                reviewId, templateId, snapshotId);
            return ApiResponse.success(reviewVersion);
            
        } catch (Exception e) {
            return ApiResponse.error("创建检查单模板版本关联失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取检查单的模板版本信息
     */
    @GetMapping("/reviews/{reviewId}/template-version")
    public ApiResponse<ReviewTemplateVersion> getReviewTemplateVersion(@PathVariable String reviewId) {
        try {
            ReviewTemplateVersion reviewVersion = templateVersionService.getReviewTemplateVersion(reviewId);
            if (reviewVersion == null) {
                return ApiResponse.error("检查单模板版本信息不存在");
            }
            return ApiResponse.success(reviewVersion);
            
        } catch (Exception e) {
            return ApiResponse.error("获取检查单模板版本信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 比较两个版本
     */
    @PostMapping("/compare")
    public ApiResponse<VersionComparison> compareVersions(@RequestBody Map<String, Object> request) {
        try {
            String currentSnapshotId = (String) request.get("currentSnapshotId");
            String targetSnapshotId = (String) request.get("targetSnapshotId");
            
            if (currentSnapshotId == null || currentSnapshotId.trim().isEmpty() ||
                targetSnapshotId == null || targetSnapshotId.trim().isEmpty()) {
                return ApiResponse.error("快照ID不能为空");
            }
            
            VersionComparison comparison = templateVersionService.compareVersions(currentSnapshotId, targetSnapshotId);
            return ApiResponse.success(comparison);
            
        } catch (Exception e) {
            return ApiResponse.error("比较版本失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查检查单是否需要版本升级
     */
    @GetMapping("/reviews/{reviewId}/check-upgrade")
    public ApiResponse<Map<String, Object>> checkUpgradeAvailable(@PathVariable String reviewId) {
        try {
            Map<String, Object> result = templateVersionService.checkUpgradeAvailable(reviewId);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("检查版本升级失败: " + e.getMessage());
        }
    }
    
    /**
     * 升级检查单到新版本
     */
    @PostMapping("/reviews/{reviewId}/upgrade")
    public ApiResponse<Map<String, Object>> upgradeReviewVersion(
            @PathVariable String reviewId, 
            @RequestBody Map<String, Object> options) {
        try {
            Map<String, Object> result = templateVersionService.upgradeReviewVersion(reviewId, options);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("版本升级失败: " + e.getMessage());
        }
    }
    
    /**
     * 回滚检查单到指定版本
     */
    @PostMapping("/reviews/{reviewId}/rollback")
    public ApiResponse<Map<String, Object>> rollbackReviewVersion(
            @PathVariable String reviewId, 
            @RequestBody Map<String, Object> request) {
        try {
            String targetSnapshotId = (String) request.get("targetSnapshotId");
            @SuppressWarnings("unchecked")
            Map<String, Object> options = (Map<String, Object>) request.get("options");
            
            if (targetSnapshotId == null || targetSnapshotId.trim().isEmpty()) {
                return ApiResponse.error("目标快照ID不能为空");
            }
            
            Map<String, Object> result = templateVersionService.rollbackReviewVersion(reviewId, targetSnapshotId, options);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("版本回滚失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取检查单的版本历史
     */
    @GetMapping("/reviews/{reviewId}/history")
    public ApiResponse<List<Map<String, Object>>> getReviewVersionHistory(@PathVariable String reviewId) {
        try {
            List<Map<String, Object>> history = templateVersionService.getReviewVersionHistory(reviewId);
            return ApiResponse.success(history);
            
        } catch (Exception e) {
            return ApiResponse.error("获取版本历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取模板版本管理配置
     */
    @GetMapping("/templates/{templateId}/config")
    public ApiResponse<Map<String, Object>> getVersionConfig(@PathVariable String templateId) {
        try {
            Map<String, Object> config = templateVersionService.getVersionConfig(templateId);
            return ApiResponse.success(config);
            
        } catch (Exception e) {
            return ApiResponse.error("获取版本配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新模板版本管理配置
     */
    @PutMapping("/templates/{templateId}/config")
    public ApiResponse<String> updateVersionConfig(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> versionConfig) {
        try {
            templateVersionService.updateVersionConfig(templateId, versionConfig);
            return ApiResponse.success("版本配置更新成功");
            
        } catch (Exception e) {
            return ApiResponse.error("更新版本配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 自动创建快照（基于配置规则）
     */
    @PostMapping("/templates/{templateId}/auto-snapshot")
    public ApiResponse<TemplateSnapshot> autoCreateSnapshot(
            @PathVariable String templateId, 
            @RequestBody Map<String, Object> request) {
        try {
            String changeType = (String) request.get("changeType");
            TemplateSnapshot snapshot = templateVersionService.autoCreateSnapshot(templateId, changeType);
            return ApiResponse.success(snapshot);
            
        } catch (Exception e) {
            return ApiResponse.error("自动创建快照失败: " + e.getMessage());
        }
    }
    
    /**
     * 清理过期快照
     */
    @PostMapping("/templates/{templateId}/cleanup")
    public ApiResponse<Map<String, Object>> cleanupSnapshots(
            @PathVariable String templateId, 
            @RequestBody(required = false) Map<String, Object> options) {
        try {
            boolean dryRun = options != null && Boolean.TRUE.equals(options.get("dryRun"));
            Map<String, Object> result = templateVersionService.cleanupSnapshots(templateId, dryRun);
            return ApiResponse.success(result);
            
        } catch (Exception e) {
            return ApiResponse.error("清理快照失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出模板快照
     */
    @GetMapping("/snapshots/{snapshotId}/export")
    public ApiResponse<String> exportSnapshot(@PathVariable String snapshotId) {
        try {
            String exportData = templateVersionService.exportSnapshot(snapshotId);
            return ApiResponse.success(exportData);
            
        } catch (Exception e) {
            return ApiResponse.error("导出快照失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取使用特定快照的检查单列表
     */
    @GetMapping("/snapshots/{snapshotId}/usage")
    public ApiResponse<List<Map<String, Object>>> getSnapshotUsage(@PathVariable String snapshotId) {
        try {
            List<Map<String, Object>> usage = templateVersionService.getSnapshotUsage(snapshotId);
            return ApiResponse.success(usage);
            
        } catch (Exception e) {
            return ApiResponse.error("获取快照使用情况失败: " + e.getMessage());
        }
    }
}
