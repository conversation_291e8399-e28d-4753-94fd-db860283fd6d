package com.checklist.service;

import com.checklist.model.TemplateSnapshot;
import com.checklist.model.ReviewTemplateVersion;
import com.checklist.model.VersionComparison;
import com.checklist.util.JsonFileUtil;
import com.checklist.util.VersionUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 模板版本管理服务
 */
@Service
public class TemplateVersionService {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private TemplateService templateService;
    
    @Autowired
    private AdminConfigService adminConfigService;
    
    @Autowired
    private JsonFileUtil jsonFileUtil;
    
    private static final String SNAPSHOTS_DIR = "data/template-snapshots";
    private static final String REVIEW_VERSIONS_DIR = "data/review-template-versions";
    private static final String VERSION_CONFIGS_DIR = "data/template-version-configs";
    
    /**
     * 创建模板快照
     */
    public TemplateSnapshot createSnapshot(String templateId, String version, String description) throws Exception {
        // 获取模板信息
        JsonNode templateData = templateService.getTemplateById(templateId);
        if (templateData == null) {
            throw new RuntimeException("模板不存在");
        }
        
        // 获取模板配置
        JsonNode configData = adminConfigService.getTemplateConfig(templateId);
        
        // 获取检查项
        JsonNode itemsData = templateService.getTemplateItems(templateId);
        
        // 创建快照
        TemplateSnapshot snapshot = new TemplateSnapshot(templateId, version, "system", templateData, itemsData, configData);
        snapshot.setDescription(description);
        
        // 保存到文件
        String fileName = snapshot.getId() + ".json";
        jsonFileUtil.saveToFile(SNAPSHOTS_DIR, fileName, snapshot);
        
        return snapshot;
    }
    
    /**
     * 获取模板的所有快照
     */
    public Map<String, Object> getTemplateSnapshots(String templateId, int limit, int offset, boolean includeContent) throws Exception {
        List<TemplateSnapshot> allSnapshots = new ArrayList<>();
        
        // 读取所有快照文件
        File snapshotsDir = new File(SNAPSHOTS_DIR);
        if (snapshotsDir.exists()) {
            File[] files = snapshotsDir.listFiles((dir, name) -> name.endsWith(".json"));
            if (files != null) {
                for (File file : files) {
                    try {
                        TemplateSnapshot snapshot = jsonFileUtil.loadFromFile(file, TemplateSnapshot.class);
                        if (templateId.equals(snapshot.getTemplateId())) {
                            if (!includeContent) {
                                // 不包含详细内容，清空大字段
                                snapshot.setTemplateData(null);
                                snapshot.setItemsData(null);
                                snapshot.setConfigData(null);
                            }
                            allSnapshots.add(snapshot);
                        }
                    } catch (Exception e) {
                        // 忽略损坏的文件
                    }
                }
            }
        }
        
        // 按创建时间倒序排序
        allSnapshots.sort((a, b) -> b.getCreatedTime().compareTo(a.getCreatedTime()));
        
        // 分页
        int total = allSnapshots.size();
        int endIndex = Math.min(offset + limit, total);
        List<TemplateSnapshot> snapshots = allSnapshots.subList(offset, endIndex);
        
        Map<String, Object> result = new HashMap<>();
        result.put("snapshots", snapshots);
        result.put("total", total);
        result.put("hasMore", offset + limit < total);
        
        return result;
    }
    
    /**
     * 获取特定快照详情
     */
    public TemplateSnapshot getSnapshot(String snapshotId) throws Exception {
        String fileName = snapshotId + ".json";
        return jsonFileUtil.loadFromFile(SNAPSHOTS_DIR, fileName, TemplateSnapshot.class);
    }
    
    /**
     * 删除快照
     */
    public boolean deleteSnapshot(String snapshotId) throws Exception {
        String fileName = snapshotId + ".json";
        return jsonFileUtil.deleteFile(SNAPSHOTS_DIR, fileName);
    }
    
    /**
     * 为检查单创建模板版本关联
     */
    public ReviewTemplateVersion createReviewTemplateVersion(String reviewId, String templateId, String snapshotId) throws Exception {
        TemplateSnapshot snapshot;
        
        if (snapshotId == null || snapshotId.trim().isEmpty()) {
            // 如果没有指定快照ID，创建新快照
            String version = generateNextVersion(templateId);
            snapshot = createSnapshot(templateId, version, "检查单 " + reviewId + " 创建时的模板快照");
            snapshotId = snapshot.getId();
        } else {
            // 验证快照是否存在
            snapshot = getSnapshot(snapshotId);
            if (snapshot == null) {
                throw new RuntimeException("快照不存在");
            }
        }
        
        // 创建关联关系
        ReviewTemplateVersion reviewVersion = new ReviewTemplateVersion(reviewId, templateId, snapshotId, snapshot.getVersion());
        reviewVersion.setSnapshot(snapshot);
        
        // 保存到文件
        String fileName = reviewId + ".json";
        jsonFileUtil.saveToFile(REVIEW_VERSIONS_DIR, fileName, reviewVersion);
        
        return reviewVersion;
    }
    
    /**
     * 获取检查单的模板版本信息
     */
    public ReviewTemplateVersion getReviewTemplateVersion(String reviewId) throws Exception {
        String fileName = reviewId + ".json";
        ReviewTemplateVersion reviewVersion = jsonFileUtil.loadFromFile(REVIEW_VERSIONS_DIR, fileName, ReviewTemplateVersion.class);
        
        if (reviewVersion != null && reviewVersion.getSnapshot() == null) {
            // 如果快照信息为空，尝试加载
            try {
                TemplateSnapshot snapshot = getSnapshot(reviewVersion.getSnapshotId());
                reviewVersion.setSnapshot(snapshot);
            } catch (Exception e) {
                // 忽略快照加载失败
            }
        }
        
        return reviewVersion;
    }
    
    /**
     * 比较两个版本
     */
    public VersionComparison compareVersions(String currentSnapshotId, String targetSnapshotId) throws Exception {
        TemplateSnapshot currentSnapshot = getSnapshot(currentSnapshotId);
        TemplateSnapshot targetSnapshot = getSnapshot(targetSnapshotId);
        
        if (currentSnapshot == null || targetSnapshot == null) {
            throw new RuntimeException("快照不存在");
        }
        
        return VersionUtil.compareSnapshots(currentSnapshot, targetSnapshot);
    }
    
    /**
     * 检查检查单是否需要版本升级
     */
    public Map<String, Object> checkUpgradeAvailable(String reviewId) throws Exception {
        ReviewTemplateVersion reviewVersion = getReviewTemplateVersion(reviewId);
        if (reviewVersion == null) {
            throw new RuntimeException("检查单模板版本信息不存在");
        }
        
        // 获取模板的最新快照
        Map<String, Object> snapshotsResult = getTemplateSnapshots(reviewVersion.getTemplateId(), 1, 0, false);
        @SuppressWarnings("unchecked")
        List<TemplateSnapshot> snapshots = (List<TemplateSnapshot>) snapshotsResult.get("snapshots");
        
        Map<String, Object> result = new HashMap<>();
        
        if (snapshots.isEmpty()) {
            result.put("upgradeAvailable", false);
            result.put("currentVersion", reviewVersion.getVersion());
            result.put("latestVersion", reviewVersion.getVersion());
            result.put("latestSnapshotId", reviewVersion.getSnapshotId());
        } else {
            TemplateSnapshot latestSnapshot = snapshots.get(0);
            boolean upgradeAvailable = !latestSnapshot.getId().equals(reviewVersion.getSnapshotId());
            
            result.put("upgradeAvailable", upgradeAvailable);
            result.put("currentVersion", reviewVersion.getVersion());
            result.put("latestVersion", latestSnapshot.getVersion());
            result.put("latestSnapshotId", latestSnapshot.getId());
            
            // 如果有升级，提供变更信息
            if (upgradeAvailable) {
                VersionComparison comparison = compareVersions(reviewVersion.getSnapshotId(), latestSnapshot.getId());
                result.put("changes", comparison);
            }
        }
        
        return result;
    }
    
    /**
     * 升级检查单到新版本
     */
    public Map<String, Object> upgradeReviewVersion(String reviewId, Map<String, Object> options) throws Exception {
        String targetSnapshotId = (String) options.get("targetSnapshotId");
        if (targetSnapshotId == null || targetSnapshotId.trim().isEmpty()) {
            throw new RuntimeException("目标快照ID不能为空");
        }
        
        ReviewTemplateVersion reviewVersion = getReviewTemplateVersion(reviewId);
        if (reviewVersion == null) {
            throw new RuntimeException("检查单版本信息不存在");
        }
        
        TemplateSnapshot targetSnapshot = getSnapshot(targetSnapshotId);
        if (targetSnapshot == null) {
            throw new RuntimeException("目标快照不存在");
        }
        
        // 创建备份（如果需要）
        String backupId = null;
        @SuppressWarnings("unchecked")
        Map<String, Object> backupOptions = (Map<String, Object>) options.get("backup");
        if (backupOptions != null && Boolean.TRUE.equals(backupOptions.get("createBackup"))) {
            backupId = createReviewBackup(reviewId, backupOptions);
        }
        
        // 执行升级
        Map<String, Object> upgradeResult = performUpgrade(reviewId, reviewVersion.getSnapshot(), targetSnapshot, options);
        
        // 更新版本关联
        reviewVersion.setSnapshotId(targetSnapshotId);
        reviewVersion.setVersion(targetSnapshot.getVersion());
        reviewVersion.setSnapshot(targetSnapshot);
        
        // 保存更新后的版本信息
        String fileName = reviewId + ".json";
        jsonFileUtil.saveToFile(REVIEW_VERSIONS_DIR, fileName, reviewVersion);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", upgradeResult.get("success"));
        result.put("reviewId", reviewId);
        result.put("oldSnapshotId", reviewVersion.getSnapshotId());
        result.put("newSnapshotId", targetSnapshotId);
        result.put("changes", upgradeResult.get("changes"));
        result.put("conflicts", upgradeResult.get("conflicts"));
        result.put("errors", upgradeResult.get("errors"));
        result.put("warnings", upgradeResult.get("warnings"));
        
        if (backupId != null) {
            Map<String, Object> backup = new HashMap<>();
            backup.put("backupId", backupId);
            result.put("backup", backup);
        }
        
        return result;
    }
    
    /**
     * 回滚检查单到指定版本
     */
    public Map<String, Object> rollbackReviewVersion(String reviewId, String targetSnapshotId, Map<String, Object> options) throws Exception {
        // 回滚逻辑与升级类似，但是方向相反
        Map<String, Object> upgradeOptions = new HashMap<>();
        upgradeOptions.put("targetSnapshotId", targetSnapshotId);
        upgradeOptions.put("backup", options);
        
        return upgradeReviewVersion(reviewId, upgradeOptions);
    }
    
    /**
     * 获取检查单的版本历史
     */
    public List<Map<String, Object>> getReviewVersionHistory(String reviewId) throws Exception {
        // 这里可以实现版本历史记录功能
        // 目前返回当前版本信息
        List<Map<String, Object>> history = new ArrayList<>();
        
        ReviewTemplateVersion reviewVersion = getReviewTemplateVersion(reviewId);
        if (reviewVersion != null) {
            Map<String, Object> historyItem = new HashMap<>();
            historyItem.put("snapshotId", reviewVersion.getSnapshotId());
            historyItem.put("version", reviewVersion.getVersion());
            historyItem.put("createdTime", reviewVersion.getCreatedTime());
            historyItem.put("changeType", "create");
            history.add(historyItem);
        }
        
        return history;
    }
    
    /**
     * 获取模板版本管理配置
     */
    public Map<String, Object> getVersionConfig(String templateId) throws Exception {
        String fileName = templateId + ".json";
        Map<String, Object> config = jsonFileUtil.loadFromFile(VERSION_CONFIGS_DIR, fileName, Map.class);
        
        if (config == null) {
            // 返回默认配置
            config = createDefaultVersionConfig();
        }
        
        return config;
    }
    
    /**
     * 更新模板版本管理配置
     */
    public void updateVersionConfig(String templateId, Map<String, Object> versionConfig) throws Exception {
        String fileName = templateId + ".json";
        jsonFileUtil.saveToFile(VERSION_CONFIGS_DIR, fileName, versionConfig);
    }
    
    /**
     * 自动创建快照（基于配置规则）
     */
    public TemplateSnapshot autoCreateSnapshot(String templateId, String changeType) throws Exception {
        Map<String, Object> config = getVersionConfig(templateId);
        @SuppressWarnings("unchecked")
        Map<String, Object> autoSnapshot = (Map<String, Object>) config.get("autoSnapshot");
        
        if (autoSnapshot == null || !Boolean.TRUE.equals(autoSnapshot.get("enabled"))) {
            return null;
        }
        
        // 检查是否需要创建快照
        boolean shouldCreate = false;
        switch (changeType) {
            case "template":
                shouldCreate = Boolean.TRUE.equals(autoSnapshot.get("onTemplateChange"));
                break;
            case "config":
                shouldCreate = Boolean.TRUE.equals(autoSnapshot.get("onConfigChange"));
                break;
            case "publish":
                shouldCreate = Boolean.TRUE.equals(autoSnapshot.get("onPublish"));
                break;
        }
        
        if (!shouldCreate) {
            return null;
        }
        
        // 检查最小间隔
        // 这里可以实现时间间隔检查逻辑
        
        // 生成版本号
        String version = generateNextVersion(templateId);
        
        // 创建快照
        return createSnapshot(templateId, version, "自动快照 - " + changeType + " 变更");
    }
    
    /**
     * 清理过期快照
     */
    public Map<String, Object> cleanupSnapshots(String templateId, boolean dryRun) throws Exception {
        Map<String, Object> config = getVersionConfig(templateId);
        @SuppressWarnings("unchecked")
        Map<String, Object> retention = (Map<String, Object>) config.get("retention");
        
        List<Map<String, Object>> toDelete = new ArrayList<>();
        
        if (retention != null) {
            // 获取所有快照
            Map<String, Object> snapshotsResult = getTemplateSnapshots(templateId, Integer.MAX_VALUE, 0, false);
            @SuppressWarnings("unchecked")
            List<TemplateSnapshot> snapshots = (List<TemplateSnapshot>) snapshotsResult.get("snapshots");
            
            // 按数量限制清理
            Integer maxVersions = (Integer) retention.get("maxVersions");
            if (maxVersions != null && maxVersions > 0 && snapshots.size() > maxVersions) {
                List<TemplateSnapshot> excessSnapshots = snapshots.subList(maxVersions, snapshots.size());
                for (TemplateSnapshot snapshot : excessSnapshots) {
                    Map<String, Object> deleteItem = new HashMap<>();
                    deleteItem.put("snapshotId", snapshot.getId());
                    deleteItem.put("version", snapshot.getVersion());
                    deleteItem.put("reason", "超出数量限制");
                    toDelete.add(deleteItem);
                }
            }
        }
        
        // 执行删除
        int deletedCount = 0;
        if (!dryRun) {
            for (Map<String, Object> item : toDelete) {
                String snapshotId = (String) item.get("snapshotId");
                if (deleteSnapshot(snapshotId)) {
                    deletedCount++;
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("toDelete", toDelete);
        if (!dryRun) {
            result.put("deleted", deletedCount);
        }
        
        return result;
    }
    
    /**
     * 导出模板快照
     */
    public String exportSnapshot(String snapshotId) throws Exception {
        TemplateSnapshot snapshot = getSnapshot(snapshotId);
        if (snapshot == null) {
            throw new RuntimeException("快照不存在");
        }
        
        return objectMapper.writeValueAsString(snapshot);
    }
    
    /**
     * 获取使用特定快照的检查单列表
     */
    public List<Map<String, Object>> getSnapshotUsage(String snapshotId) throws Exception {
        List<Map<String, Object>> usage = new ArrayList<>();
        
        // 遍历所有检查单版本文件
        File reviewVersionsDir = new File(REVIEW_VERSIONS_DIR);
        if (reviewVersionsDir.exists()) {
            File[] files = reviewVersionsDir.listFiles((dir, name) -> name.endsWith(".json"));
            if (files != null) {
                for (File file : files) {
                    try {
                        ReviewTemplateVersion reviewVersion = jsonFileUtil.loadFromFile(file, ReviewTemplateVersion.class);
                        if (snapshotId.equals(reviewVersion.getSnapshotId())) {
                            Map<String, Object> usageItem = new HashMap<>();
                            usageItem.put("reviewId", reviewVersion.getReviewId());
                            usageItem.put("reviewName", "检查单 " + reviewVersion.getReviewId());
                            usageItem.put("status", "active");
                            usageItem.put("createdTime", reviewVersion.getCreatedTime());
                            usageItem.put("createdBy", "system");
                            usage.add(usageItem);
                        }
                    } catch (Exception e) {
                        // 忽略损坏的文件
                    }
                }
            }
        }
        
        return usage;
    }
    
    // 私有辅助方法
    
    private String generateNextVersion(String templateId) throws Exception {
        Map<String, Object> snapshotsResult = getTemplateSnapshots(templateId, 1, 0, false);
        @SuppressWarnings("unchecked")
        List<TemplateSnapshot> snapshots = (List<TemplateSnapshot>) snapshotsResult.get("snapshots");
        
        String currentVersion = snapshots.isEmpty() ? null : snapshots.get(0).getVersion();
        return VersionUtil.generateNextVersion(currentVersion, "minor");
    }
    
    private Map<String, Object> createDefaultVersionConfig() {
        Map<String, Object> config = new HashMap<>();
        
        Map<String, Object> autoSnapshot = new HashMap<>();
        autoSnapshot.put("enabled", true);
        autoSnapshot.put("onTemplateChange", true);
        autoSnapshot.put("onConfigChange", true);
        autoSnapshot.put("onPublish", true);
        autoSnapshot.put("minInterval", 60);
        config.put("autoSnapshot", autoSnapshot);
        
        Map<String, Object> retention = new HashMap<>();
        retention.put("maxVersions", 50);
        retention.put("maxAge", 365);
        retention.put("keepMajorVersions", true);
        config.put("retention", retention);
        
        Map<String, Object> versioning = new HashMap<>();
        versioning.put("scheme", "semantic");
        versioning.put("autoIncrement", true);
        config.put("versioning", versioning);
        
        return config;
    }
    
    private String createReviewBackup(String reviewId, Map<String, Object> backupOptions) throws Exception {
        // 创建检查单备份的逻辑
        String backupId = "backup_" + System.currentTimeMillis();
        
        // 这里可以实现具体的备份逻辑
        // 比如复制检查单数据到备份目录
        
        return backupId;
    }
    
    private Map<String, Object> performUpgrade(String reviewId, TemplateSnapshot currentSnapshot, 
                                             TemplateSnapshot targetSnapshot, Map<String, Object> options) throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        
        Map<String, Object> changes = new HashMap<>();
        changes.put("itemsAdded", 0);
        changes.put("itemsRemoved", 0);
        changes.put("itemsModified", 0);
        changes.put("configsUpdated", 0);
        result.put("changes", changes);
        
        result.put("conflicts", new ArrayList<>());
        result.put("errors", new ArrayList<>());
        result.put("warnings", new ArrayList<>());
        
        // 这里可以实现具体的升级逻辑
        // 比较快照差异，更新检查单数据等
        
        return result;
    }
}
