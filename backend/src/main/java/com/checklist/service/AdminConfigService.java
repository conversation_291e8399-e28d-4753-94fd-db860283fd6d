package com.checklist.service;

import com.checklist.util.JsonFileUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.*;

/**
 * 管理配置服务
 */
@Service
public class AdminConfigService {
    
    @Autowired
    private JsonFileUtil jsonFileUtil;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private static final String BUTTON_GROUPS_DIR = "data/admin-configs/button-groups";
    private static final String DEFECT_RULES_DIR = "data/admin-configs/defect-rules";
    private static final String TEMPLATE_CONFIGS_DIR = "data/admin-configs/template-configs";
    
    /**
     * 获取模板的状态按钮组配置
     */
    public List<Map<String, Object>> getTemplateButtonGroups(String templateId) throws Exception {
        String fileName = templateId + "_buttons.json";
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> buttonGroups = jsonFileUtil.loadFromFile(BUTTON_GROUPS_DIR, fileName, List.class);
        
        if (buttonGroups == null) {
            // 返回默认按钮组配置
            buttonGroups = createDefaultButtonGroups();
        }
        
        return buttonGroups;
    }
    
    /**
     * 保存模板的状态按钮组配置
     */
    public void saveTemplateButtonGroups(String templateId, List<Map<String, Object>> buttonGroups) throws Exception {
        String fileName = templateId + "_buttons.json";
        jsonFileUtil.saveToFile(BUTTON_GROUPS_DIR, fileName, buttonGroups);
    }
    
    /**
     * 获取模板的缺陷规则配置
     */
    public List<Map<String, Object>> getTemplateDefectRules(String templateId) throws Exception {
        String fileName = templateId + "_defects.json";
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> defectRules = jsonFileUtil.loadFromFile(DEFECT_RULES_DIR, fileName, List.class);
        
        if (defectRules == null) {
            // 返回默认缺陷规则配置
            defectRules = createDefaultDefectRules();
        }
        
        return defectRules;
    }
    
    /**
     * 保存模板的缺陷规则配置
     */
    public void saveTemplateDefectRules(String templateId, List<Map<String, Object>> defectRules) throws Exception {
        String fileName = templateId + "_defects.json";
        jsonFileUtil.saveToFile(DEFECT_RULES_DIR, fileName, defectRules);
    }
    
    /**
     * 获取模板的完整配置
     */
    public Map<String, Object> getTemplateConfig(String templateId) throws Exception {
        Map<String, Object> config = new HashMap<>();
        
        // 获取按钮组配置
        List<Map<String, Object>> buttonGroups = getTemplateButtonGroups(templateId);
        config.put("buttonGroups", buttonGroups);
        
        // 获取缺陷规则配置
        List<Map<String, Object>> defectRules = getTemplateDefectRules(templateId);
        config.put("defectRules", defectRules);
        
        // 获取自定义字段配置
        List<Map<String, Object>> customFields = getTemplateCustomFields(templateId);
        config.put("customFields", customFields);
        
        return config;
    }
    
    /**
     * 保存模板的完整配置
     */
    public void saveTemplateConfig(String templateId, Map<String, Object> config) throws Exception {
        // 保存按钮组配置
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> buttonGroups = (List<Map<String, Object>>) config.get("buttonGroups");
        if (buttonGroups != null) {
            saveTemplateButtonGroups(templateId, buttonGroups);
        }
        
        // 保存缺陷规则配置
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> defectRules = (List<Map<String, Object>>) config.get("defectRules");
        if (defectRules != null) {
            saveTemplateDefectRules(templateId, defectRules);
        }
        
        // 保存自定义字段配置
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> customFields = (List<Map<String, Object>>) config.get("customFields");
        if (customFields != null) {
            saveTemplateCustomFields(templateId, customFields);
        }
    }
    
    /**
     * 创建状态按钮组
     */
    public Map<String, Object> createButtonGroup(Map<String, Object> buttonGroup) throws Exception {
        String id = "group_" + System.currentTimeMillis();
        buttonGroup.put("id", id);
        
        // 这里可以保存到全局按钮组配置中
        return buttonGroup;
    }
    
    /**
     * 更新状态按钮组
     */
    public Map<String, Object> updateButtonGroup(String groupId, Map<String, Object> buttonGroup) throws Exception {
        buttonGroup.put("id", groupId);
        
        // 这里可以更新全局按钮组配置
        return buttonGroup;
    }
    
    /**
     * 删除状态按钮组
     */
    public boolean deleteButtonGroup(String groupId) throws Exception {
        // 这里可以从全局按钮组配置中删除
        return true;
    }
    
    /**
     * 创建缺陷规则
     */
    public Map<String, Object> createDefectRule(Map<String, Object> defectRule) throws Exception {
        String id = "rule_" + System.currentTimeMillis();
        defectRule.put("id", id);
        
        // 这里可以保存到全局缺陷规则配置中
        return defectRule;
    }
    
    /**
     * 更新缺陷规则
     */
    public Map<String, Object> updateDefectRule(String ruleId, Map<String, Object> defectRule) throws Exception {
        defectRule.put("id", ruleId);
        
        // 这里可以更新全局缺陷规则配置
        return defectRule;
    }
    
    /**
     * 删除缺陷规则
     */
    public boolean deleteDefectRule(String ruleId) throws Exception {
        // 这里可以从全局缺陷规则配置中删除
        return true;
    }
    
    /**
     * 测试缺陷规则
     */
    public Map<String, Object> testDefectRule(String ruleId, Map<String, Object> testData) throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("matched", true);
        
        // 模拟生成的缺陷
        Map<String, Object> generatedDefect = new HashMap<>();
        generatedDefect.put("title", "【" + testData.get("category") + "】" + testData.get("content"));
        generatedDefect.put("description", "检查内容：" + testData.get("content") + "\n不通过原因：" + testData.get("comment"));
        generatedDefect.put("severity", "medium");
        generatedDefect.put("category", "general");
        
        result.put("generatedDefect", generatedDefect);
        
        return result;
    }
    
    /**
     * 获取所有可用的API接口列表
     */
    public List<Map<String, Object>> getAvailableApiEndpoints() throws Exception {
        List<Map<String, Object>> endpoints = new ArrayList<>();
        
        // 状态更新接口
        Map<String, Object> statusEndpoint = new HashMap<>();
        statusEndpoint.put("endpoint", "/api/review/items/{itemId}/status");
        statusEndpoint.put("method", "PUT");
        statusEndpoint.put("description", "更新检查项状态");
        
        List<Map<String, Object>> statusParams = new ArrayList<>();
        statusParams.add(createParameter("status", "string", true, "状态值"));
        statusParams.add(createParameter("comment", "string", false, "备注"));
        statusParams.add(createParameter("reviewer", "string", false, "评审人"));
        statusParams.add(createParameter("reviewTime", "string", false, "评审时间"));
        statusEndpoint.put("parameters", statusParams);
        
        endpoints.add(statusEndpoint);
        
        // 条件通过接口
        Map<String, Object> conditionalEndpoint = new HashMap<>();
        conditionalEndpoint.put("endpoint", "/api/review/items/{itemId}/conditional-pass");
        conditionalEndpoint.put("method", "PUT");
        conditionalEndpoint.put("description", "条件通过");
        
        List<Map<String, Object>> conditionalParams = new ArrayList<>();
        conditionalParams.add(createParameter("comment", "string", true, "通过条件"));
        conditionalParams.add(createParameter("conditions", "string", false, "具体条件"));
        conditionalEndpoint.put("parameters", conditionalParams);
        
        endpoints.add(conditionalEndpoint);
        
        return endpoints;
    }
    
    /**
     * 验证模板配置
     */
    public Map<String, Object> validateTemplateConfig(String templateId, Map<String, Object> config) throws Exception {
        List<Map<String, Object>> errors = new ArrayList<>();
        List<Map<String, Object>> warnings = new ArrayList<>();
        
        // 验证按钮组配置
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> buttonGroups = (List<Map<String, Object>>) config.get("buttonGroups");
        if (buttonGroups != null) {
            for (int i = 0; i < buttonGroups.size(); i++) {
                Map<String, Object> group = buttonGroups.get(i);
                if (group.get("name") == null || group.get("name").toString().trim().isEmpty()) {
                    Map<String, Object> error = new HashMap<>();
                    error.put("type", "button");
                    error.put("id", group.get("id"));
                    error.put("message", "按钮组名称不能为空");
                    error.put("severity", "error");
                    errors.add(error);
                }
            }
        }
        
        // 验证缺陷规则配置
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> defectRules = (List<Map<String, Object>>) config.get("defectRules");
        if (defectRules != null) {
            for (int i = 0; i < defectRules.size(); i++) {
                Map<String, Object> rule = defectRules.get(i);
                if (rule.get("name") == null || rule.get("name").toString().trim().isEmpty()) {
                    Map<String, Object> error = new HashMap<>();
                    error.put("type", "rule");
                    error.put("id", rule.get("id"));
                    error.put("message", "规则名称不能为空");
                    error.put("severity", "error");
                    errors.add(error);
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("valid", errors.isEmpty());
        result.put("errors", errors);
        result.put("warnings", warnings);
        
        return result;
    }
    
    // 私有辅助方法
    
    private List<Map<String, Object>> getTemplateCustomFields(String templateId) throws Exception {
        // 这里可以实现获取自定义字段配置的逻辑
        return new ArrayList<>();
    }
    
    private void saveTemplateCustomFields(String templateId, List<Map<String, Object>> customFields) throws Exception {
        // 这里可以实现保存自定义字段配置的逻辑
    }
    
    private List<Map<String, Object>> createDefaultButtonGroups() {
        List<Map<String, Object>> buttonGroups = new ArrayList<>();
        
        Map<String, Object> defaultGroup = new HashMap<>();
        defaultGroup.put("id", "default");
        defaultGroup.put("name", "默认状态按钮");
        defaultGroup.put("description", "默认的状态按钮组");
        defaultGroup.put("layout", "horizontal");
        
        List<Map<String, Object>> buttons = new ArrayList<>();
        
        // 通过按钮
        Map<String, Object> passButton = new HashMap<>();
        passButton.put("id", "pass");
        passButton.put("label", "通过");
        passButton.put("status", "PASS");
        passButton.put("type", "success");
        passButton.put("order", 1);
        passButton.put("enabled", true);
        
        Map<String, Object> passAction = new HashMap<>();
        passAction.put("apiEndpoint", "/api/review/items/{itemId}/status");
        passAction.put("method", "PUT");
        passAction.put("requireComment", false);
        passAction.put("successMessage", "标记为通过");
        
        Map<String, Object> passPayload = new HashMap<>();
        passPayload.put("status", "PASS");
        passPayload.put("reviewer", "${currentUser}");
        passPayload.put("reviewTime", "${currentTime}");
        passAction.put("payloadTemplate", passPayload);
        
        passButton.put("action", passAction);
        buttons.add(passButton);
        
        // 不通过按钮
        Map<String, Object> failButton = new HashMap<>();
        failButton.put("id", "fail");
        failButton.put("label", "不通过");
        failButton.put("status", "FAIL");
        failButton.put("type", "danger");
        failButton.put("order", 2);
        failButton.put("enabled", true);
        
        Map<String, Object> failAction = new HashMap<>();
        failAction.put("apiEndpoint", "/api/review/items/{itemId}/status");
        failAction.put("method", "PUT");
        failAction.put("requireComment", true);
        failAction.put("confirmMessage", "确认标记为不通过？");
        failAction.put("successMessage", "标记为不通过");
        
        Map<String, Object> failPayload = new HashMap<>();
        failPayload.put("status", "FAIL");
        failPayload.put("comment", "${comment}");
        failPayload.put("reviewer", "${currentUser}");
        failPayload.put("reviewTime", "${currentTime}");
        failAction.put("payloadTemplate", failPayload);
        
        failButton.put("action", failAction);
        buttons.add(failButton);
        
        defaultGroup.put("buttons", buttons);
        buttonGroups.add(defaultGroup);
        
        return buttonGroups;
    }
    
    private List<Map<String, Object>> createDefaultDefectRules() {
        List<Map<String, Object>> defectRules = new ArrayList<>();
        
        Map<String, Object> defaultRule = new HashMap<>();
        defaultRule.put("id", "default_fail_rule");
        defaultRule.put("name", "不通过自动生成缺陷");
        defaultRule.put("description", "当检查项标记为不通过时自动生成缺陷");
        defaultRule.put("enabled", true);
        
        Map<String, Object> trigger = new HashMap<>();
        trigger.put("status", Arrays.asList("FAIL"));
        defaultRule.put("trigger", trigger);
        
        Map<String, Object> template = new HashMap<>();
        template.put("titleTemplate", "【${category}】${content}");
        template.put("descriptionTemplate", "检查内容：${content}\n不通过原因：${comment}");
        
        Map<String, Object> severityMapping = new HashMap<>();
        severityMapping.put("安全", "high");
        severityMapping.put("功能", "medium");
        severityMapping.put("性能", "medium");
        severityMapping.put("界面", "low");
        severityMapping.put("default", "medium");
        template.put("severityMapping", severityMapping);
        
        Map<String, Object> categoryMapping = new HashMap<>();
        categoryMapping.put("安全检查", "security");
        categoryMapping.put("功能检查", "functional");
        categoryMapping.put("性能检查", "performance");
        categoryMapping.put("界面检查", "ui");
        categoryMapping.put("default", "general");
        template.put("categoryMapping", categoryMapping);
        
        template.put("customFieldMapping", new HashMap<>());
        defaultRule.put("template", template);
        
        Map<String, Object> options = new HashMap<>();
        options.put("autoGenerate", false);
        options.put("requireConfirmation", true);
        options.put("batchGenerate", true);
        defaultRule.put("options", options);
        
        defectRules.add(defaultRule);
        
        return defectRules;
    }
    
    private Map<String, Object> createParameter(String name, String type, boolean required, String description) {
        Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        param.put("type", type);
        param.put("required", required);
        param.put("description", description);
        return param;
    }
}
