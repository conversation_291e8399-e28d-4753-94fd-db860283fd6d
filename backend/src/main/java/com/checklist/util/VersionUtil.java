package com.checklist.util;

import com.checklist.model.TemplateSnapshot;
import com.checklist.model.VersionComparison;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.*;

/**
 * 版本管理工具类
 */
public class VersionUtil {
    
    /**
     * 生成下一个版本号
     */
    public static String generateNextVersion(String currentVersion, String changeType) {
        if (currentVersion == null || currentVersion.trim().isEmpty()) {
            return "1.0.0";
        }
        
        try {
            String[] parts = currentVersion.split("\\.");
            if (parts.length >= 3) {
                int major = Integer.parseInt(parts[0]);
                int minor = Integer.parseInt(parts[1]);
                int patch = Integer.parseInt(parts[2]);
                
                switch (changeType.toLowerCase()) {
                    case "major":
                        return (major + 1) + ".0.0";
                    case "minor":
                        return major + "." + (minor + 1) + ".0";
                    case "patch":
                        return major + "." + minor + "." + (patch + 1);
                    default:
                        return major + "." + (minor + 1) + ".0";
                }
            }
        } catch (NumberFormatException e) {
            // 如果解析失败，返回默认版本
        }
        
        return "1.0.0";
    }
    
    /**
     * 比较两个快照的差异
     */
    public static VersionComparison compareSnapshots(TemplateSnapshot currentSnapshot, TemplateSnapshot targetSnapshot) {
        VersionComparison comparison = new VersionComparison();
        comparison.setTemplateId(currentSnapshot.getTemplateId());
        comparison.setCurrentVersion(currentSnapshot.getVersion());
        comparison.setTargetVersion(targetSnapshot.getVersion());
        
        VersionComparison.VersionChanges changes = new VersionComparison.VersionChanges();
        changes.setTemplate(new ArrayList<>());
        changes.setItems(new ArrayList<>());
        changes.setConfig(new ArrayList<>());
        
        // 比较模板基本信息
        compareTemplateData(currentSnapshot.getTemplateData(), targetSnapshot.getTemplateData(), changes.getTemplate());
        
        // 比较检查项
        compareItemsData(currentSnapshot.getItemsData(), targetSnapshot.getItemsData(), changes.getItems());
        
        // 比较配置
        compareConfigData(currentSnapshot.getConfigData(), targetSnapshot.getConfigData(), changes.getConfig());
        
        comparison.setChanges(changes);
        
        // 计算统计信息
        VersionComparison.ChangeSummary summary = new VersionComparison.ChangeSummary();
        summary.setTotalChanges(changes.getTemplate().size() + changes.getItems().size() + changes.getConfig().size());
        summary.setAddedItems((int) changes.getItems().stream().filter(c -> "added".equals(c.get("type"))).count());
        summary.setRemovedItems((int) changes.getItems().stream().filter(c -> "removed".equals(c.get("type"))).count());
        summary.setModifiedItems((int) changes.getItems().stream().filter(c -> "modified".equals(c.get("type"))).count());
        summary.setConfigChanges(changes.getConfig().size());
        
        comparison.setSummary(summary);
        
        return comparison;
    }
    
    /**
     * 比较模板基本信息
     */
    private static void compareTemplateData(JsonNode currentData, JsonNode targetData, List<Map<String, Object>> changes) {
        if (currentData == null || targetData == null) return;
        
        String[] fields = {"name", "description", "category", "type"};
        for (String field : fields) {
            String oldValue = currentData.has(field) ? currentData.get(field).asText() : "";
            String newValue = targetData.has(field) ? targetData.get(field).asText() : "";
            
            if (!oldValue.equals(newValue)) {
                Map<String, Object> change = new HashMap<>();
                change.put("type", "modified");
                change.put("field", field);
                change.put("oldValue", oldValue);
                change.put("newValue", newValue);
                change.put("description", String.format("模板%s从\"%s\"变更为\"%s\"", field, oldValue, newValue));
                changes.add(change);
            }
        }
    }
    
    /**
     * 比较检查项数据
     */
    private static void compareItemsData(JsonNode currentData, JsonNode targetData, List<Map<String, Object>> changes) {
        if (currentData == null || targetData == null) return;
        
        // 将检查项转换为Map，以ID为键
        Map<String, JsonNode> currentItems = new HashMap<>();
        Map<String, JsonNode> targetItems = new HashMap<>();
        
        if (currentData.isArray()) {
            for (JsonNode item : currentData) {
                if (item.has("id")) {
                    currentItems.put(item.get("id").asText(), item);
                }
            }
        }
        
        if (targetData.isArray()) {
            for (JsonNode item : targetData) {
                if (item.has("id")) {
                    targetItems.put(item.get("id").asText(), item);
                }
            }
        }
        
        // 检查新增和修改的项
        for (Map.Entry<String, JsonNode> entry : targetItems.entrySet()) {
            String itemId = entry.getKey();
            JsonNode targetItem = entry.getValue();
            JsonNode currentItem = currentItems.get(itemId);
            
            if (currentItem == null) {
                // 新增项
                Map<String, Object> change = new HashMap<>();
                change.put("type", "added");
                change.put("itemId", itemId);
                change.put("sequence", targetItem.has("sequence") ? targetItem.get("sequence").asInt() : 0);
                change.put("content", targetItem.has("content") ? targetItem.get("content").asText() : "");
                change.put("description", "新增检查项: " + (targetItem.has("content") ? targetItem.get("content").asText() : ""));
                changes.add(change);
            } else {
                // 检查修改
                String[] itemFields = {"content", "required", "category", "sequence"};
                for (String field : itemFields) {
                    String oldValue = currentItem.has(field) ? currentItem.get(field).asText() : "";
                    String newValue = targetItem.has(field) ? targetItem.get(field).asText() : "";
                    
                    if (!oldValue.equals(newValue)) {
                        Map<String, Object> change = new HashMap<>();
                        change.put("type", "modified");
                        change.put("itemId", itemId);
                        change.put("field", field);
                        change.put("oldValue", oldValue);
                        change.put("newValue", newValue);
                        String content = currentItem.has("content") ? currentItem.get("content").asText() : "";
                        change.put("description", String.format("检查项\"%s\"的%s从\"%s\"变更为\"%s\"", content, field, oldValue, newValue));
                        changes.add(change);
                    }
                }
            }
        }
        
        // 检查删除的项
        for (Map.Entry<String, JsonNode> entry : currentItems.entrySet()) {
            String itemId = entry.getKey();
            JsonNode currentItem = entry.getValue();
            
            if (!targetItems.containsKey(itemId)) {
                Map<String, Object> change = new HashMap<>();
                change.put("type", "removed");
                change.put("itemId", itemId);
                change.put("description", "删除检查项: " + (currentItem.has("content") ? currentItem.get("content").asText() : ""));
                changes.add(change);
            }
        }
    }
    
    /**
     * 比较配置数据
     */
    private static void compareConfigData(JsonNode currentData, JsonNode targetData, List<Map<String, Object>> changes) {
        if (currentData == null || targetData == null) return;
        
        // 比较按钮组配置
        if (currentData.has("buttonGroups") && targetData.has("buttonGroups")) {
            JsonNode currentButtonGroups = currentData.get("buttonGroups");
            JsonNode targetButtonGroups = targetData.get("buttonGroups");
            
            if (!currentButtonGroups.equals(targetButtonGroups)) {
                Map<String, Object> change = new HashMap<>();
                change.put("type", "modified");
                change.put("category", "buttonGroup");
                change.put("description", "按钮组配置已变更");
                changes.add(change);
            }
        }
        
        // 比较缺陷规则配置
        if (currentData.has("defectRules") && targetData.has("defectRules")) {
            JsonNode currentDefectRules = currentData.get("defectRules");
            JsonNode targetDefectRules = targetData.get("defectRules");
            
            if (!currentDefectRules.equals(targetDefectRules)) {
                Map<String, Object> change = new HashMap<>();
                change.put("type", "modified");
                change.put("category", "defectRule");
                change.put("description", "缺陷规则配置已变更");
                changes.add(change);
            }
        }
    }
    
    /**
     * 版本号比较
     */
    public static int compareVersions(String version1, String version2) {
        if (version1 == null && version2 == null) return 0;
        if (version1 == null) return -1;
        if (version2 == null) return 1;
        
        try {
            String[] parts1 = version1.split("\\.");
            String[] parts2 = version2.split("\\.");
            
            int maxLength = Math.max(parts1.length, parts2.length);
            
            for (int i = 0; i < maxLength; i++) {
                int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
                int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;
                
                if (num1 < num2) return -1;
                if (num1 > num2) return 1;
            }
            
            return 0;
        } catch (NumberFormatException e) {
            return version1.compareTo(version2);
        }
    }
    
    /**
     * 检查是否为主版本
     */
    public static boolean isMajorVersion(String version) {
        if (version == null) return false;
        
        try {
            String[] parts = version.split("\\.");
            if (parts.length >= 3) {
                return "0".equals(parts[1]) && "0".equals(parts[2]);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        
        return false;
    }
}
