from flask import Blueprint, request, jsonify, current_app
from sqlalchemy.orm import sessionmaker
from sqlalchemy import desc, and_
from datetime import datetime, timedelta
import json

from models.template_version import (
    TemplateSnapshot, ReviewTemplateVersion, TemplateVersionConfig, 
    VersionUpgradeLog, generate_version, compare_snapshots
)
from models.template import Template
from models.review import Review
from services.template_service import TemplateService
from services.admin_config_service import AdminConfigService
from utils.auth import require_auth
from utils.response import success_response, error_response

template_version_bp = Blueprint('template_version', __name__, url_prefix='/api/template-version')

@template_version_bp.route('/snapshots', methods=['POST'])
@require_auth
def create_snapshot():
    """创建模板快照"""
    try:
        data = request.get_json()
        template_id = data.get('templateId')
        version = data.get('version')
        description = data.get('description')
        
        if not template_id or not version:
            return error_response('模板ID和版本号不能为空', 400)
        
        # 获取模板信息
        template_service = TemplateService()
        template = template_service.get_template(template_id)
        if not template:
            return error_response('模板不存在', 404)
        
        # 获取模板配置
        admin_config_service = AdminConfigService()
        config = admin_config_service.get_template_config(template_id)
        
        # 创建快照
        snapshot = TemplateSnapshot(
            template_id=template_id,
            version=version,
            created_by=request.user.id,
            template_data=template.to_dict(),
            items_data=[item.to_dict() for item in template.items],
            config_data=config,
            description=description
        )
        
        # 保存到数据库
        db_session = current_app.db_session
        db_session.add(snapshot)
        db_session.commit()
        
        return success_response(snapshot.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"创建模板快照失败: {str(e)}")
        return error_response('创建模板快照失败', 500)


@template_version_bp.route('/templates/<template_id>/snapshots', methods=['GET'])
@require_auth
def get_template_snapshots(template_id):
    """获取模板的所有快照"""
    try:
        limit = request.args.get('limit', 20, type=int)
        offset = request.args.get('offset', 0, type=int)
        include_content = request.args.get('includeContent', 'false').lower() == 'true'
        
        db_session = current_app.db_session
        
        # 查询快照
        query = db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.template_id == template_id
        ).order_by(desc(TemplateSnapshot.created_time))
        
        total = query.count()
        snapshots = query.offset(offset).limit(limit).all()
        
        # 转换为字典
        snapshot_list = []
        for snapshot in snapshots:
            snapshot_dict = snapshot.to_dict()
            if not include_content:
                # 不包含详细内容，只返回基本信息
                snapshot_dict.pop('template', None)
                snapshot_dict.pop('items', None)
                snapshot_dict.pop('config', None)
            snapshot_list.append(snapshot_dict)
        
        return success_response({
            'snapshots': snapshot_list,
            'total': total,
            'hasMore': offset + limit < total
        })
        
    except Exception as e:
        current_app.logger.error(f"获取模板快照失败: {str(e)}")
        return error_response('获取模板快照失败', 500)


@template_version_bp.route('/snapshots/<snapshot_id>', methods=['GET'])
@require_auth
def get_snapshot(snapshot_id):
    """获取特定快照详情"""
    try:
        db_session = current_app.db_session
        snapshot = db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.id == snapshot_id
        ).first()
        
        if not snapshot:
            return error_response('快照不存在', 404)
        
        return success_response(snapshot.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"获取快照详情失败: {str(e)}")
        return error_response('获取快照详情失败', 500)


@template_version_bp.route('/review-versions', methods=['POST'])
@require_auth
def create_review_template_version():
    """为检查单创建模板版本关联"""
    try:
        data = request.get_json()
        review_id = data.get('reviewId')
        template_id = data.get('templateId')
        snapshot_id = data.get('snapshotId')
        
        if not review_id or not template_id:
            return error_response('检查单ID和模板ID不能为空', 400)
        
        db_session = current_app.db_session
        
        # 检查检查单是否存在
        review = db_session.query(Review).filter(Review.id == review_id).first()
        if not review:
            return error_response('检查单不存在', 404)
        
        # 如果没有指定快照ID，创建新快照
        if not snapshot_id:
            # 获取模板信息
            template_service = TemplateService()
            template = template_service.get_template(template_id)
            if not template:
                return error_response('模板不存在', 404)
            
            # 获取版本配置
            version_config = db_session.query(TemplateVersionConfig).filter(
                TemplateVersionConfig.template_id == template_id
            ).first()
            
            # 生成版本号
            latest_snapshot = db_session.query(TemplateSnapshot).filter(
                TemplateSnapshot.template_id == template_id
            ).order_by(desc(TemplateSnapshot.created_time)).first()
            
            scheme = version_config.versioning_scheme if version_config else 'semantic'
            current_version = latest_snapshot.version if latest_snapshot else None
            version = generate_version(scheme, current_version, 'minor')
            
            # 获取模板配置
            admin_config_service = AdminConfigService()
            config = admin_config_service.get_template_config(template_id)
            
            # 创建快照
            snapshot = TemplateSnapshot(
                template_id=template_id,
                version=version,
                created_by=request.user.id,
                template_data=template.to_dict(),
                items_data=[item.to_dict() for item in template.items],
                config_data=config,
                description=f'检查单 {review_id} 创建时的模板快照'
            )
            
            db_session.add(snapshot)
            db_session.flush()  # 获取快照ID
            snapshot_id = snapshot.id
        else:
            # 验证快照是否存在
            snapshot = db_session.query(TemplateSnapshot).filter(
                TemplateSnapshot.id == snapshot_id
            ).first()
            if not snapshot:
                return error_response('快照不存在', 404)
            version = snapshot.version
        
        # 创建关联关系
        review_version = ReviewTemplateVersion(
            review_id=review_id,
            template_id=template_id,
            snapshot_id=snapshot_id,
            version=version
        )
        
        db_session.add(review_version)
        db_session.commit()
        
        return success_response(review_version.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"创建检查单模板版本关联失败: {str(e)}")
        return error_response('创建检查单模板版本关联失败', 500)


@template_version_bp.route('/reviews/<review_id>/template-version', methods=['GET'])
@require_auth
def get_review_template_version(review_id):
    """获取检查单的模板版本信息"""
    try:
        db_session = current_app.db_session
        review_version = db_session.query(ReviewTemplateVersion).filter(
            ReviewTemplateVersion.review_id == review_id
        ).first()
        
        if not review_version:
            return error_response('检查单模板版本信息不存在', 404)
        
        return success_response(review_version.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"获取检查单模板版本信息失败: {str(e)}")
        return error_response('获取检查单模板版本信息失败', 500)


@template_version_bp.route('/compare', methods=['POST'])
@require_auth
def compare_versions():
    """比较两个版本"""
    try:
        data = request.get_json()
        current_snapshot_id = data.get('currentSnapshotId')
        target_snapshot_id = data.get('targetSnapshotId')
        
        if not current_snapshot_id or not target_snapshot_id:
            return error_response('快照ID不能为空', 400)
        
        db_session = current_app.db_session
        
        # 获取快照
        current_snapshot = db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.id == current_snapshot_id
        ).first()
        target_snapshot = db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.id == target_snapshot_id
        ).first()
        
        if not current_snapshot or not target_snapshot:
            return error_response('快照不存在', 404)
        
        # 比较版本
        comparison = compare_snapshots(current_snapshot, target_snapshot)
        
        return success_response(comparison)
        
    except Exception as e:
        current_app.logger.error(f"比较版本失败: {str(e)}")
        return error_response('比较版本失败', 500)


@template_version_bp.route('/reviews/<review_id>/check-upgrade', methods=['GET'])
@require_auth
def check_upgrade_available(review_id):
    """检查检查单是否需要版本升级"""
    try:
        db_session = current_app.db_session
        
        # 获取检查单的当前版本
        review_version = db_session.query(ReviewTemplateVersion).filter(
            ReviewTemplateVersion.review_id == review_id
        ).first()
        
        if not review_version:
            return error_response('检查单模板版本信息不存在', 404)
        
        # 获取模板的最新快照
        latest_snapshot = db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.template_id == review_version.template_id
        ).order_by(desc(TemplateSnapshot.created_time)).first()
        
        if not latest_snapshot:
            return success_response({
                'upgradeAvailable': False,
                'currentVersion': review_version.version,
                'latestVersion': review_version.version,
                'latestSnapshotId': review_version.snapshot_id
            })
        
        # 检查是否有升级
        upgrade_available = latest_snapshot.id != review_version.snapshot_id
        
        result = {
            'upgradeAvailable': upgrade_available,
            'currentVersion': review_version.version,
            'latestVersion': latest_snapshot.version,
            'latestSnapshotId': latest_snapshot.id
        }
        
        # 如果有升级，提供变更信息
        if upgrade_available:
            current_snapshot = review_version.snapshot
            comparison = compare_snapshots(current_snapshot, latest_snapshot)
            result['changes'] = comparison
        
        return success_response(result)
        
    except Exception as e:
        current_app.logger.error(f"检查版本升级失败: {str(e)}")
        return error_response('检查版本升级失败', 500)


@template_version_bp.route('/templates/<template_id>/config', methods=['GET'])
@require_auth
def get_version_config(template_id):
    """获取模板版本管理配置"""
    try:
        db_session = current_app.db_session
        config = db_session.query(TemplateVersionConfig).filter(
            TemplateVersionConfig.template_id == template_id
        ).first()
        
        if not config:
            # 创建默认配置
            config = TemplateVersionConfig(template_id)
            db_session.add(config)
            db_session.commit()
        
        return success_response(config.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"获取版本配置失败: {str(e)}")
        return error_response('获取版本配置失败', 500)


@template_version_bp.route('/templates/<template_id>/config', methods=['PUT'])
@require_auth
def update_version_config(template_id):
    """更新模板版本管理配置"""
    try:
        data = request.get_json()
        
        db_session = current_app.db_session
        config = db_session.query(TemplateVersionConfig).filter(
            TemplateVersionConfig.template_id == template_id
        ).first()
        
        if not config:
            config = TemplateVersionConfig(template_id)
            db_session.add(config)
        
        # 更新配置
        auto_snapshot = data.get('autoSnapshot', {})
        retention = data.get('retention', {})
        versioning = data.get('versioning', {})
        
        config.auto_snapshot_enabled = auto_snapshot.get('enabled', True)
        config.auto_snapshot_on_template_change = auto_snapshot.get('onTemplateChange', True)
        config.auto_snapshot_on_config_change = auto_snapshot.get('onConfigChange', True)
        config.auto_snapshot_on_publish = auto_snapshot.get('onPublish', True)
        config.auto_snapshot_min_interval = auto_snapshot.get('minInterval', 60)
        
        config.max_versions = retention.get('maxVersions', 50)
        config.max_age_days = retention.get('maxAge', 365)
        config.keep_major_versions = retention.get('keepMajorVersions', True)
        
        config.versioning_scheme = versioning.get('scheme', 'semantic')
        config.auto_increment = versioning.get('autoIncrement', True)
        config.version_prefix = versioning.get('prefix')
        
        db_session.commit()
        
        return success_response(config.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"更新版本配置失败: {str(e)}")
        return error_response('更新版本配置失败', 500)


@template_version_bp.route('/templates/<template_id>/auto-snapshot', methods=['POST'])
@require_auth
def auto_create_snapshot(template_id):
    """自动创建快照（基于配置规则）"""
    try:
        data = request.get_json()
        change_type = data.get('changeType')  # template, config, publish
        
        db_session = current_app.db_session
        
        # 获取版本配置
        config = db_session.query(TemplateVersionConfig).filter(
            TemplateVersionConfig.template_id == template_id
        ).first()
        
        if not config or not config.auto_snapshot_enabled:
            return success_response(None)
        
        # 检查是否需要创建快照
        should_create = False
        if change_type == 'template' and config.auto_snapshot_on_template_change:
            should_create = True
        elif change_type == 'config' and config.auto_snapshot_on_config_change:
            should_create = True
        elif change_type == 'publish' and config.auto_snapshot_on_publish:
            should_create = True
        
        if not should_create:
            return success_response(None)
        
        # 检查最小间隔
        latest_snapshot = db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.template_id == template_id
        ).order_by(desc(TemplateSnapshot.created_time)).first()
        
        if latest_snapshot:
            time_diff = datetime.utcnow() - latest_snapshot.created_time
            if time_diff.total_seconds() < config.auto_snapshot_min_interval * 60:
                return success_response(None)
        
        # 创建快照
        template_service = TemplateService()
        template = template_service.get_template(template_id)
        if not template:
            return error_response('模板不存在', 404)
        
        # 生成版本号
        current_version = latest_snapshot.version if latest_snapshot else None
        version = generate_version(config.versioning_scheme, current_version, 'minor')
        
        # 获取模板配置
        admin_config_service = AdminConfigService()
        template_config = admin_config_service.get_template_config(template_id)
        
        # 创建快照
        snapshot = TemplateSnapshot(
            template_id=template_id,
            version=version,
            created_by=request.user.id,
            template_data=template.to_dict(),
            items_data=[item.to_dict() for item in template.items],
            config_data=template_config,
            description=f'自动快照 - {change_type} 变更'
        )
        
        db_session.add(snapshot)
        db_session.commit()
        
        return success_response(snapshot.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"自动创建快照失败: {str(e)}")
        return error_response('自动创建快照失败', 500)
