from flask import Blueprint, request, jsonify, current_app
from sqlalchemy.orm import sessionmaker
from sqlalchemy import desc, and_
import json

from models.admin_config import StatusButtonGroup, DefectRule, CustomField
from services.admin_config_service import AdminConfigService
from utils.auth import require_auth, require_admin
from utils.response import success_response, error_response
from utils.validation import validate_button_group, validate_defect_rule

admin_config_bp = Blueprint('admin_config', __name__, url_prefix='/api/admin/config')

@admin_config_bp.route('/templates/<template_id>/button-groups', methods=['GET'])
@require_auth
def get_template_button_groups(template_id):
    """获取模板的状态按钮组配置"""
    try:
        service = AdminConfigService(current_app.db_session)
        button_groups = service.get_template_button_groups(template_id)
        
        return success_response([group.to_dict() for group in button_groups])
        
    except Exception as e:
        current_app.logger.error(f"获取模板按钮组配置失败: {str(e)}")
        return error_response('获取模板按钮组配置失败', 500)


@admin_config_bp.route('/templates/<template_id>/button-groups', methods=['PUT'])
@require_admin
def save_template_button_groups(template_id):
    """保存模板的状态按钮组配置"""
    try:
        data = request.get_json()
        button_groups_data = data.get('buttonGroups', [])
        
        # 验证数据
        for group_data in button_groups_data:
            errors = validate_button_group(group_data)
            if errors:
                return error_response(f'按钮组配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        service.save_template_button_groups(template_id, button_groups_data)
        
        return success_response({'message': '按钮组配置保存成功'})
        
    except Exception as e:
        current_app.logger.error(f"保存模板按钮组配置失败: {str(e)}")
        return error_response('保存模板按钮组配置失败', 500)


@admin_config_bp.route('/templates/<template_id>/defect-rules', methods=['GET'])
@require_auth
def get_template_defect_rules(template_id):
    """获取模板的缺陷规则配置"""
    try:
        service = AdminConfigService(current_app.db_session)
        defect_rules = service.get_template_defect_rules(template_id)
        
        return success_response([rule.to_dict() for rule in defect_rules])
        
    except Exception as e:
        current_app.logger.error(f"获取模板缺陷规则配置失败: {str(e)}")
        return error_response('获取模板缺陷规则配置失败', 500)


@admin_config_bp.route('/templates/<template_id>/defect-rules', methods=['PUT'])
@require_admin
def save_template_defect_rules(template_id):
    """保存模板的缺陷规则配置"""
    try:
        data = request.get_json()
        defect_rules_data = data.get('defectRules', [])
        
        # 验证数据
        for rule_data in defect_rules_data:
            errors = validate_defect_rule(rule_data)
            if errors:
                return error_response(f'缺陷规则配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        service.save_template_defect_rules(template_id, defect_rules_data)
        
        return success_response({'message': '缺陷规则配置保存成功'})
        
    except Exception as e:
        current_app.logger.error(f"保存模板缺陷规则配置失败: {str(e)}")
        return error_response('保存模板缺陷规则配置失败', 500)


@admin_config_bp.route('/templates/<template_id>/config', methods=['GET'])
@require_auth
def get_template_config(template_id):
    """获取模板的完整配置"""
    try:
        service = AdminConfigService(current_app.db_session)
        config = service.get_template_config(template_id)
        
        return success_response(config)
        
    except Exception as e:
        current_app.logger.error(f"获取模板配置失败: {str(e)}")
        return error_response('获取模板配置失败', 500)


@admin_config_bp.route('/templates/<template_id>/config', methods=['PUT'])
@require_admin
def save_template_config(template_id):
    """保存模板的完整配置"""
    try:
        data = request.get_json()
        
        # 验证按钮组配置
        button_groups_data = data.get('buttonGroups', [])
        for group_data in button_groups_data:
            errors = validate_button_group(group_data)
            if errors:
                return error_response(f'按钮组配置验证失败: {"; ".join(errors)}', 400)
        
        # 验证缺陷规则配置
        defect_rules_data = data.get('defectRules', [])
        for rule_data in defect_rules_data:
            errors = validate_defect_rule(rule_data)
            if errors:
                return error_response(f'缺陷规则配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        service.save_template_config(template_id, data)
        
        return success_response({'message': '模板配置保存成功'})
        
    except Exception as e:
        current_app.logger.error(f"保存模板配置失败: {str(e)}")
        return error_response('保存模板配置失败', 500)


@admin_config_bp.route('/button-groups', methods=['POST'])
@require_admin
def create_button_group():
    """创建状态按钮组"""
    try:
        data = request.get_json()
        
        # 验证数据
        errors = validate_button_group(data)
        if errors:
            return error_response(f'按钮组配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        button_group = service.create_button_group(data)
        
        return success_response(button_group.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"创建按钮组失败: {str(e)}")
        return error_response('创建按钮组失败', 500)


@admin_config_bp.route('/button-groups/<group_id>', methods=['PUT'])
@require_admin
def update_button_group(group_id):
    """更新状态按钮组"""
    try:
        data = request.get_json()
        
        # 验证数据
        errors = validate_button_group(data)
        if errors:
            return error_response(f'按钮组配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        button_group = service.update_button_group(group_id, data)
        
        if not button_group:
            return error_response('按钮组不存在', 404)
        
        return success_response(button_group.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"更新按钮组失败: {str(e)}")
        return error_response('更新按钮组失败', 500)


@admin_config_bp.route('/button-groups/<group_id>', methods=['DELETE'])
@require_admin
def delete_button_group(group_id):
    """删除状态按钮组"""
    try:
        service = AdminConfigService(current_app.db_session)
        success = service.delete_button_group(group_id)
        
        if not success:
            return error_response('按钮组不存在', 404)
        
        return success_response({'message': '按钮组删除成功'})
        
    except Exception as e:
        current_app.logger.error(f"删除按钮组失败: {str(e)}")
        return error_response('删除按钮组失败', 500)


@admin_config_bp.route('/defect-rules', methods=['POST'])
@require_admin
def create_defect_rule():
    """创建缺陷规则"""
    try:
        data = request.get_json()
        
        # 验证数据
        errors = validate_defect_rule(data)
        if errors:
            return error_response(f'缺陷规则配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        defect_rule = service.create_defect_rule(data)
        
        return success_response(defect_rule.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"创建缺陷规则失败: {str(e)}")
        return error_response('创建缺陷规则失败', 500)


@admin_config_bp.route('/defect-rules/<rule_id>', methods=['PUT'])
@require_admin
def update_defect_rule(rule_id):
    """更新缺陷规则"""
    try:
        data = request.get_json()
        
        # 验证数据
        errors = validate_defect_rule(data)
        if errors:
            return error_response(f'缺陷规则配置验证失败: {"; ".join(errors)}', 400)
        
        service = AdminConfigService(current_app.db_session)
        defect_rule = service.update_defect_rule(rule_id, data)
        
        if not defect_rule:
            return error_response('缺陷规则不存在', 404)
        
        return success_response(defect_rule.to_dict())
        
    except Exception as e:
        current_app.logger.error(f"更新缺陷规则失败: {str(e)}")
        return error_response('更新缺陷规则失败', 500)


@admin_config_bp.route('/defect-rules/<rule_id>', methods=['DELETE'])
@require_admin
def delete_defect_rule(rule_id):
    """删除缺陷规则"""
    try:
        service = AdminConfigService(current_app.db_session)
        success = service.delete_defect_rule(rule_id)
        
        if not success:
            return error_response('缺陷规则不存在', 404)
        
        return success_response({'message': '缺陷规则删除成功'})
        
    except Exception as e:
        current_app.logger.error(f"删除缺陷规则失败: {str(e)}")
        return error_response('删除缺陷规则失败', 500)


@admin_config_bp.route('/defect-rules/<rule_id>/test', methods=['POST'])
@require_auth
def test_defect_rule(rule_id):
    """测试缺陷规则"""
    try:
        data = request.get_json()
        test_data = {
            'content': data.get('content', ''),
            'category': data.get('category', ''),
            'status': data.get('status', ''),
            'comment': data.get('comment', ''),
            'customFields': data.get('customFields', {})
        }
        
        service = AdminConfigService(current_app.db_session)
        result = service.test_defect_rule(rule_id, test_data)
        
        return success_response(result)
        
    except Exception as e:
        current_app.logger.error(f"测试缺陷规则失败: {str(e)}")
        return error_response('测试缺陷规则失败', 500)


@admin_config_bp.route('/api-endpoints', methods=['GET'])
@require_auth
def get_available_api_endpoints():
    """获取所有可用的API接口列表"""
    try:
        # 这里返回系统中可用的API接口列表
        # 用于按钮配置时选择API接口
        endpoints = [
            {
                'endpoint': '/api/review/items/{itemId}/status',
                'method': 'PUT',
                'description': '更新检查项状态',
                'parameters': [
                    {'name': 'status', 'type': 'string', 'required': True, 'description': '状态值'},
                    {'name': 'comment', 'type': 'string', 'required': False, 'description': '备注'},
                    {'name': 'reviewer', 'type': 'string', 'required': False, 'description': '评审人'},
                    {'name': 'reviewTime', 'type': 'string', 'required': False, 'description': '评审时间'}
                ]
            },
            {
                'endpoint': '/api/review/items/{itemId}/conditional-pass',
                'method': 'PUT',
                'description': '条件通过',
                'parameters': [
                    {'name': 'comment', 'type': 'string', 'required': True, 'description': '通过条件'},
                    {'name': 'conditions', 'type': 'string', 'required': False, 'description': '具体条件'}
                ]
            },
            {
                'endpoint': '/api/review/items/{itemId}/partial-pass',
                'method': 'PUT',
                'description': '部分通过',
                'parameters': [
                    {'name': 'comment', 'type': 'string', 'required': True, 'description': '部分通过说明'},
                    {'name': 'partialDetails', 'type': 'string', 'required': False, 'description': '部分通过详情'}
                ]
            }
        ]
        
        return success_response(endpoints)
        
    except Exception as e:
        current_app.logger.error(f"获取API接口列表失败: {str(e)}")
        return error_response('获取API接口列表失败', 500)


@admin_config_bp.route('/templates/<template_id>/validate', methods=['POST'])
@require_auth
def validate_template_config(template_id):
    """验证模板配置"""
    try:
        data = request.get_json()
        
        errors = []
        warnings = []
        
        # 验证按钮组配置
        button_groups = data.get('buttonGroups', [])
        for i, group_data in enumerate(button_groups):
            group_errors = validate_button_group(group_data)
            for error in group_errors:
                errors.append({
                    'type': 'button',
                    'id': group_data.get('id', f'group_{i}'),
                    'message': error,
                    'severity': 'error'
                })
        
        # 验证缺陷规则配置
        defect_rules = data.get('defectRules', [])
        for i, rule_data in enumerate(defect_rules):
            rule_errors = validate_defect_rule(rule_data)
            for error in rule_errors:
                errors.append({
                    'type': 'rule',
                    'id': rule_data.get('id', f'rule_{i}'),
                    'message': error,
                    'severity': 'error'
                })
        
        # 检查警告
        if len(button_groups) == 0:
            warnings.append({
                'type': 'general',
                'id': 'no_button_groups',
                'message': '没有配置状态按钮组'
            })
        
        if len(defect_rules) == 0:
            warnings.append({
                'type': 'general',
                'id': 'no_defect_rules',
                'message': '没有配置缺陷生成规则'
            })
        
        return success_response({
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        })
        
    except Exception as e:
        current_app.logger.error(f"验证模板配置失败: {str(e)}")
        return error_response('验证模板配置失败', 500)
