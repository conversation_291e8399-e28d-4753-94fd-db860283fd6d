from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>SO<PERSON>, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime
import json
import hashlib

Base = declarative_base()

class TemplateSnapshot(Base):
    """模板快照表"""
    __tablename__ = 'template_snapshots'
    
    id = Column(String(50), primary_key=True)
    template_id = Column(String(50), ForeignKey('templates.id'), nullable=False)
    version = Column(String(20), nullable=False)
    created_time = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(50), nullable=False)
    description = Column(Text)
    
    # 模板基本信息快照
    template_data = Column(JSON, nullable=False)
    
    # 检查项快照
    items_data = Column(JSON, nullable=False)
    
    # 配置快照
    config_data = Column(JSON, nullable=False)
    
    # 元数据
    metadata = <PERSON>umn(JSON, nullable=False)
    
    # 内容哈希，用于检测变更
    content_hash = Column(String(64), nullable=False)
    
    # 关联关系
    template = relationship("Template", back_populates="snapshots")
    review_versions = relationship("ReviewTemplateVersion", back_populates="snapshot")
    
    def __init__(self, template_id, version, created_by, template_data, items_data, config_data, description=None):
        self.id = f"snapshot_{int(datetime.utcnow().timestamp() * 1000)}"
        self.template_id = template_id
        self.version = version
        self.created_by = created_by
        self.description = description
        self.template_data = template_data
        self.items_data = items_data
        self.config_data = config_data
        
        # 计算元数据
        self.metadata = {
            'item_count': len(items_data),
            'required_item_count': len([item for item in items_data if item.get('required', False)]),
            'category_count': len(set(item.get('category', '') for item in items_data)),
            'button_group_count': len(config_data.get('buttonGroups', [])),
            'defect_rule_count': len(config_data.get('defectRules', []))
        }
        
        # 计算内容哈希
        content_for_hash = {
            'template': template_data,
            'items': items_data,
            'config': config_data
        }
        content_str = json.dumps(content_for_hash, sort_keys=True)
        self.content_hash = hashlib.sha256(content_str.encode()).hexdigest()
    
    def to_dict(self):
        return {
            'id': self.id,
            'templateId': self.template_id,
            'version': self.version,
            'createdTime': self.created_time.isoformat(),
            'createdBy': self.created_by,
            'description': self.description,
            'template': self.template_data,
            'items': self.items_data,
            'config': self.config_data,
            'metadata': self.metadata
        }


class ReviewTemplateVersion(Base):
    """检查单模板版本关联表"""
    __tablename__ = 'review_template_versions'
    
    id = Column(String(50), primary_key=True)
    review_id = Column(String(50), ForeignKey('reviews.id'), nullable=False, unique=True)
    template_id = Column(String(50), nullable=False)
    snapshot_id = Column(String(50), ForeignKey('template_snapshots.id'), nullable=False)
    version = Column(String(20), nullable=False)
    created_time = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    review = relationship("Review", back_populates="template_version")
    snapshot = relationship("TemplateSnapshot", back_populates="review_versions")
    
    def __init__(self, review_id, template_id, snapshot_id, version):
        self.id = f"rtv_{int(datetime.utcnow().timestamp() * 1000)}"
        self.review_id = review_id
        self.template_id = template_id
        self.snapshot_id = snapshot_id
        self.version = version
    
    def to_dict(self):
        return {
            'reviewId': self.review_id,
            'templateId': self.template_id,
            'snapshotId': self.snapshot_id,
            'version': self.version,
            'createdTime': self.created_time.isoformat(),
            'snapshot': self.snapshot.to_dict() if self.snapshot else None
        }


class TemplateVersionConfig(Base):
    """模板版本管理配置表"""
    __tablename__ = 'template_version_configs'
    
    id = Column(String(50), primary_key=True)
    template_id = Column(String(50), ForeignKey('templates.id'), nullable=False, unique=True)
    
    # 自动快照配置
    auto_snapshot_enabled = Column(Boolean, default=True)
    auto_snapshot_on_template_change = Column(Boolean, default=True)
    auto_snapshot_on_config_change = Column(Boolean, default=True)
    auto_snapshot_on_publish = Column(Boolean, default=True)
    auto_snapshot_min_interval = Column(Integer, default=60)  # 分钟
    
    # 版本保留策略
    max_versions = Column(Integer, default=50)
    max_age_days = Column(Integer, default=365)
    keep_major_versions = Column(Boolean, default=True)
    
    # 版本号策略
    versioning_scheme = Column(String(20), default='semantic')  # semantic, timestamp, sequential
    auto_increment = Column(Boolean, default=True)
    version_prefix = Column(String(10))
    
    created_time = Column(DateTime, default=datetime.utcnow)
    updated_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    template = relationship("Template", back_populates="version_config")
    
    def __init__(self, template_id):
        self.id = f"tvc_{int(datetime.utcnow().timestamp() * 1000)}"
        self.template_id = template_id
    
    def to_dict(self):
        return {
            'templateId': self.template_id,
            'autoSnapshot': {
                'enabled': self.auto_snapshot_enabled,
                'onTemplateChange': self.auto_snapshot_on_template_change,
                'onConfigChange': self.auto_snapshot_on_config_change,
                'onPublish': self.auto_snapshot_on_publish,
                'minInterval': self.auto_snapshot_min_interval
            },
            'retention': {
                'maxVersions': self.max_versions,
                'maxAge': self.max_age_days,
                'keepMajorVersions': self.keep_major_versions
            },
            'versioning': {
                'scheme': self.versioning_scheme,
                'autoIncrement': self.auto_increment,
                'prefix': self.version_prefix
            }
        }


class VersionUpgradeLog(Base):
    """版本升级日志表"""
    __tablename__ = 'version_upgrade_logs'
    
    id = Column(String(50), primary_key=True)
    review_id = Column(String(50), ForeignKey('reviews.id'), nullable=False)
    old_snapshot_id = Column(String(50), nullable=False)
    new_snapshot_id = Column(String(50), nullable=False)
    upgrade_type = Column(String(20), nullable=False)  # upgrade, rollback
    
    # 升级结果
    success = Column(Boolean, nullable=False)
    changes_summary = Column(JSON)
    conflicts = Column(JSON)
    errors = Column(JSON)
    warnings = Column(JSON)
    
    # 备份信息
    backup_id = Column(String(50))
    backup_created_time = Column(DateTime)
    
    created_time = Column(DateTime, default=datetime.utcnow)
    created_by = Column(String(50), nullable=False)
    
    def __init__(self, review_id, old_snapshot_id, new_snapshot_id, upgrade_type, created_by):
        self.id = f"vul_{int(datetime.utcnow().timestamp() * 1000)}"
        self.review_id = review_id
        self.old_snapshot_id = old_snapshot_id
        self.new_snapshot_id = new_snapshot_id
        self.upgrade_type = upgrade_type
        self.created_by = created_by
    
    def to_dict(self):
        return {
            'id': self.id,
            'reviewId': self.review_id,
            'oldSnapshotId': self.old_snapshot_id,
            'newSnapshotId': self.new_snapshot_id,
            'upgradeType': self.upgrade_type,
            'success': self.success,
            'changesSummary': self.changes_summary,
            'conflicts': self.conflicts,
            'errors': self.errors,
            'warnings': self.warnings,
            'backupId': self.backup_id,
            'backupCreatedTime': self.backup_created_time.isoformat() if self.backup_created_time else None,
            'createdTime': self.created_time.isoformat(),
            'createdBy': self.created_by
        }


# 工具函数
def generate_version(scheme, current_version=None, change_type='minor'):
    """生成版本号"""
    if scheme == 'semantic':
        if not current_version:
            return '1.0.0'
        
        try:
            major, minor, patch = map(int, current_version.split('.'))
            if change_type == 'major':
                return f"{major + 1}.0.0"
            elif change_type == 'minor':
                return f"{major}.{minor + 1}.0"
            else:  # patch
                return f"{major}.{minor}.{patch + 1}"
        except:
            return '1.0.0'
    
    elif scheme == 'timestamp':
        return datetime.utcnow().strftime('%Y%m%d-%H%M%S')
    
    elif scheme == 'sequential':
        if not current_version:
            return '1'
        try:
            return str(int(current_version) + 1)
        except:
            return '1'
    
    return '1.0.0'


def compare_snapshots(current_snapshot, target_snapshot):
    """比较两个快照的差异"""
    changes = {
        'template': [],
        'items': [],
        'config': []
    }
    
    # 比较模板基本信息
    template_fields = ['name', 'description', 'category', 'type']
    for field in template_fields:
        old_value = current_snapshot.template_data.get(field)
        new_value = target_snapshot.template_data.get(field)
        if old_value != new_value:
            changes['template'].append({
                'type': 'modified',
                'field': field,
                'oldValue': old_value,
                'newValue': new_value,
                'description': f'模板{field}从"{old_value}"变更为"{new_value}"'
            })
    
    # 比较检查项
    current_items = {item['id']: item for item in current_snapshot.items_data}
    target_items = {item['id']: item for item in target_snapshot.items_data}
    
    # 检查新增和修改的项
    for item_id, target_item in target_items.items():
        current_item = current_items.get(item_id)
        if not current_item:
            # 新增项
            changes['items'].append({
                'type': 'added',
                'itemId': item_id,
                'sequence': target_item.get('sequence'),
                'description': f'新增检查项: {target_item.get("content", "")}'
            })
        else:
            # 检查修改
            item_fields = ['content', 'required', 'category', 'sequence']
            for field in item_fields:
                old_value = current_item.get(field)
                new_value = target_item.get(field)
                if old_value != new_value:
                    changes['items'].append({
                        'type': 'modified',
                        'itemId': item_id,
                        'field': field,
                        'oldValue': old_value,
                        'newValue': new_value,
                        'description': f'检查项"{current_item.get("content", "")}"的{field}从"{old_value}"变更为"{new_value}"'
                    })
    
    # 检查删除的项
    for item_id, current_item in current_items.items():
        if item_id not in target_items:
            changes['items'].append({
                'type': 'removed',
                'itemId': item_id,
                'description': f'删除检查项: {current_item.get("content", "")}'
            })
    
    # 计算统计信息
    summary = {
        'totalChanges': len(changes['template']) + len(changes['items']) + len(changes['config']),
        'addedItems': len([c for c in changes['items'] if c['type'] == 'added']),
        'removedItems': len([c for c in changes['items'] if c['type'] == 'removed']),
        'modifiedItems': len([c for c in changes['items'] if c['type'] == 'modified']),
        'configChanges': len(changes['config'])
    }
    
    return {
        'templateId': current_snapshot.template_id,
        'currentVersion': current_snapshot.version,
        'targetVersion': target_snapshot.version,
        'changes': changes,
        'summary': summary
    }
