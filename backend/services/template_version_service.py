from sqlalchemy.orm import sessionmaker
from sqlalchemy import desc, and_
from datetime import datetime, timedelta
import json
import copy

from models.template_version import (
    TemplateSnapshot, ReviewTemplateVersion, TemplateVersionConfig, 
    VersionUpgradeLog, generate_version, compare_snapshots
)
from models.template import Template
from models.review import Review, ReviewItem
from services.template_service import TemplateService
from services.admin_config_service import AdminConfigService


class TemplateVersionService:
    """模板版本管理服务"""
    
    def __init__(self, db_session):
        self.db_session = db_session
        self.template_service = TemplateService(db_session)
        self.admin_config_service = AdminConfigService(db_session)
    
    def create_snapshot_for_review(self, review_id, template_id):
        """为检查单创建模板快照"""
        try:
            # 获取模板信息
            template = self.template_service.get_template(template_id)
            if not template:
                raise ValueError('模板不存在')
            
            # 获取模板配置
            config = self.admin_config_service.get_template_config(template_id)
            
            # 获取版本配置
            version_config = self.db_session.query(TemplateVersionConfig).filter(
                TemplateVersionConfig.template_id == template_id
            ).first()
            
            # 生成版本号
            latest_snapshot = self.db_session.query(TemplateSnapshot).filter(
                TemplateSnapshot.template_id == template_id
            ).order_by(desc(TemplateSnapshot.created_time)).first()
            
            scheme = version_config.versioning_scheme if version_config else 'semantic'
            current_version = latest_snapshot.version if latest_snapshot else None
            version = generate_version(scheme, current_version, 'minor')
            
            # 创建快照
            snapshot = TemplateSnapshot(
                template_id=template_id,
                version=version,
                created_by='system',  # 系统自动创建
                template_data=template.to_dict(),
                items_data=[item.to_dict() for item in template.items],
                config_data=config,
                description=f'检查单 {review_id} 创建时的模板快照'
            )
            
            self.db_session.add(snapshot)
            self.db_session.flush()  # 获取快照ID
            
            # 创建关联关系
            review_version = ReviewTemplateVersion(
                review_id=review_id,
                template_id=template_id,
                snapshot_id=snapshot.id,
                version=version
            )
            
            self.db_session.add(review_version)
            self.db_session.commit()
            
            return review_version
            
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def check_upgrade_available(self, review_id):
        """检查检查单是否需要版本升级"""
        # 获取检查单的当前版本
        review_version = self.db_session.query(ReviewTemplateVersion).filter(
            ReviewTemplateVersion.review_id == review_id
        ).first()
        
        if not review_version:
            return None
        
        # 获取模板的最新快照
        latest_snapshot = self.db_session.query(TemplateSnapshot).filter(
            TemplateSnapshot.template_id == review_version.template_id
        ).order_by(desc(TemplateSnapshot.created_time)).first()
        
        if not latest_snapshot:
            return {
                'upgradeAvailable': False,
                'currentVersion': review_version.version,
                'latestVersion': review_version.version,
                'latestSnapshotId': review_version.snapshot_id
            }
        
        # 检查是否有升级
        upgrade_available = latest_snapshot.id != review_version.snapshot_id
        
        result = {
            'upgradeAvailable': upgrade_available,
            'currentVersion': review_version.version,
            'latestVersion': latest_snapshot.version,
            'latestSnapshotId': latest_snapshot.id
        }
        
        # 如果有升级，提供变更信息
        if upgrade_available:
            current_snapshot = review_version.snapshot
            comparison = compare_snapshots(current_snapshot, latest_snapshot)
            result['changes'] = comparison
        
        return result
    
    def upgrade_review_version(self, review_id, target_snapshot_id, options, user_id):
        """升级检查单到新版本"""
        try:
            # 获取检查单和当前版本
            review = self.db_session.query(Review).filter(Review.id == review_id).first()
            if not review:
                raise ValueError('检查单不存在')
            
            review_version = self.db_session.query(ReviewTemplateVersion).filter(
                ReviewTemplateVersion.review_id == review_id
            ).first()
            if not review_version:
                raise ValueError('检查单版本信息不存在')
            
            # 获取目标快照
            target_snapshot = self.db_session.query(TemplateSnapshot).filter(
                TemplateSnapshot.id == target_snapshot_id
            ).first()
            if not target_snapshot:
                raise ValueError('目标快照不存在')
            
            # 创建升级日志
            upgrade_log = VersionUpgradeLog(
                review_id=review_id,
                old_snapshot_id=review_version.snapshot_id,
                new_snapshot_id=target_snapshot_id,
                upgrade_type='upgrade',
                created_by=user_id
            )
            
            # 创建备份（如果需要）
            backup_id = None
            if options.get('backup', {}).get('createBackup', False):
                backup_id = self._create_review_backup(review, options.get('backup', {}))
                upgrade_log.backup_id = backup_id
                upgrade_log.backup_created_time = datetime.utcnow()
            
            # 执行升级
            upgrade_result = self._perform_upgrade(
                review, 
                review_version.snapshot, 
                target_snapshot, 
                options.get('strategy', {})
            )
            
            # 更新版本关联
            review_version.snapshot_id = target_snapshot_id
            review_version.version = target_snapshot.version
            
            # 更新升级日志
            upgrade_log.success = upgrade_result['success']
            upgrade_log.changes_summary = upgrade_result['changes']
            upgrade_log.conflicts = upgrade_result['conflicts']
            upgrade_log.errors = upgrade_result['errors']
            upgrade_log.warnings = upgrade_result['warnings']
            
            self.db_session.add(upgrade_log)
            self.db_session.commit()
            
            return {
                'success': upgrade_result['success'],
                'reviewId': review_id,
                'oldSnapshotId': review_version.snapshot_id,
                'newSnapshotId': target_snapshot_id,
                'changes': upgrade_result['changes'],
                'conflicts': upgrade_result['conflicts'],
                'backup': {'backupId': backup_id} if backup_id else None,
                'errors': upgrade_result['errors'],
                'warnings': upgrade_result['warnings']
            }
            
        except Exception as e:
            self.db_session.rollback()
            raise e
    
    def _perform_upgrade(self, review, current_snapshot, target_snapshot, strategy):
        """执行版本升级"""
        result = {
            'success': True,
            'changes': {'itemsAdded': 0, 'itemsRemoved': 0, 'itemsModified': 0, 'configsUpdated': 0},
            'conflicts': [],
            'errors': [],
            'warnings': []
        }
        
        try:
            # 比较版本差异
            comparison = compare_snapshots(current_snapshot, target_snapshot)
            
            # 处理检查项变更
            if strategy.get('items', {}).get('addNewItems', True):
                result['changes']['itemsAdded'] = self._add_new_items(
                    review, comparison['changes']['items'], strategy
                )
            
            if strategy.get('items', {}).get('removeDeletedItems', False):
                result['changes']['itemsRemoved'] = self._remove_deleted_items(
                    review, comparison['changes']['items'], strategy
                )
            
            if strategy.get('items', {}).get('updateModifiedItems', True):
                result['changes']['itemsModified'] = self._update_modified_items(
                    review, comparison['changes']['items'], strategy
                )
            
            # 处理配置变更
            if strategy.get('config', {}).get('updateButtonGroups', True):
                self._update_button_groups(review, target_snapshot.config_data)
                result['changes']['configsUpdated'] += 1
            
            if strategy.get('config', {}).get('updateDefectRules', True):
                self._update_defect_rules(review, target_snapshot.config_data)
                result['changes']['configsUpdated'] += 1
            
        except Exception as e:
            result['success'] = False
            result['errors'].append(str(e))
        
        return result
    
    def _add_new_items(self, review, item_changes, strategy):
        """添加新增的检查项"""
        added_count = 0
        
        for change in item_changes:
            if change['type'] == 'added':
                # 创建新的检查项
                new_item = ReviewItem(
                    review_id=review.id,
                    item_id=change['itemId'],
                    sequence=change.get('sequence', 999),
                    content=change.get('content', ''),
                    required=change.get('required', False),
                    category=change.get('category', ''),
                    status='PENDING',
                    comment='',
                    reviewer=None,
                    review_time=None
                )
                
                self.db_session.add(new_item)
                added_count += 1
        
        return added_count
    
    def _remove_deleted_items(self, review, item_changes, strategy):
        """移除已删除的检查项"""
        removed_count = 0
        
        for change in item_changes:
            if change['type'] == 'removed':
                # 查找并删除检查项
                item = self.db_session.query(ReviewItem).filter(
                    and_(
                        ReviewItem.review_id == review.id,
                        ReviewItem.item_id == change['itemId']
                    )
                ).first()
                
                if item:
                    # 如果保留用户数据，只标记为删除而不实际删除
                    if strategy.get('items', {}).get('preserveUserData', True):
                        item.status = 'DELETED'
                    else:
                        self.db_session.delete(item)
                    removed_count += 1
        
        return removed_count
    
    def _update_modified_items(self, review, item_changes, strategy):
        """更新已修改的检查项"""
        modified_count = 0
        
        for change in item_changes:
            if change['type'] == 'modified':
                # 查找检查项
                item = self.db_session.query(ReviewItem).filter(
                    and_(
                        ReviewItem.review_id == review.id,
                        ReviewItem.item_id == change['itemId']
                    )
                ).first()
                
                if item:
                    # 根据冲突处理策略更新
                    conflict_strategy = strategy.get('conflicts', {}).get('onItemConflict', 'keep-user')
                    
                    if conflict_strategy == 'use-template':
                        # 使用模板的新值
                        setattr(item, change['field'], change['newValue'])
                        modified_count += 1
                    elif conflict_strategy == 'merge':
                        # 合并策略（具体实现根据字段类型）
                        if change['field'] in ['content', 'category']:
                            setattr(item, change['field'], change['newValue'])
                            modified_count += 1
                    # 'keep-user' 策略不做任何更新
        
        return modified_count
    
    def _update_button_groups(self, review, config_data):
        """更新按钮组配置"""
        # 这里可以更新检查单关联的按钮组配置
        # 具体实现根据业务需求
        pass
    
    def _update_defect_rules(self, review, config_data):
        """更新缺陷规则配置"""
        # 这里可以更新检查单关联的缺陷规则配置
        # 具体实现根据业务需求
        pass
    
    def _create_review_backup(self, review, backup_options):
        """创建检查单备份"""
        # 创建检查单的完整备份
        # 返回备份ID
        backup_id = f"backup_{int(datetime.utcnow().timestamp() * 1000)}"
        
        # 这里实现备份逻辑
        # 可以将检查单数据序列化保存到备份表或文件系统
        
        return backup_id
    
    def cleanup_old_snapshots(self, template_id, dry_run=False):
        """清理过期快照"""
        # 获取版本配置
        config = self.db_session.query(TemplateVersionConfig).filter(
            TemplateVersionConfig.template_id == template_id
        ).first()
        
        if not config:
            return {'toDelete': [], 'deleted': 0}
        
        # 查找需要删除的快照
        to_delete = []
        
        # 按数量限制
        if config.max_versions > 0:
            snapshots = self.db_session.query(TemplateSnapshot).filter(
                TemplateSnapshot.template_id == template_id
            ).order_by(desc(TemplateSnapshot.created_time)).all()
            
            if len(snapshots) > config.max_versions:
                excess_snapshots = snapshots[config.max_versions:]
                for snapshot in excess_snapshots:
                    # 检查是否为主版本（如果配置了保留主版本）
                    if config.keep_major_versions and self._is_major_version(snapshot.version):
                        continue
                    
                    to_delete.append({
                        'snapshotId': snapshot.id,
                        'version': snapshot.version,
                        'reason': '超出数量限制'
                    })
        
        # 按时间限制
        if config.max_age_days > 0:
            cutoff_date = datetime.utcnow() - timedelta(days=config.max_age_days)
            old_snapshots = self.db_session.query(TemplateSnapshot).filter(
                and_(
                    TemplateSnapshot.template_id == template_id,
                    TemplateSnapshot.created_time < cutoff_date
                )
            ).all()
            
            for snapshot in old_snapshots:
                if config.keep_major_versions and self._is_major_version(snapshot.version):
                    continue
                
                # 检查是否有检查单在使用
                usage_count = self.db_session.query(ReviewTemplateVersion).filter(
                    ReviewTemplateVersion.snapshot_id == snapshot.id
                ).count()
                
                if usage_count == 0:
                    to_delete.append({
                        'snapshotId': snapshot.id,
                        'version': snapshot.version,
                        'reason': '超出时间限制'
                    })
        
        # 执行删除
        deleted_count = 0
        if not dry_run:
            for item in to_delete:
                snapshot = self.db_session.query(TemplateSnapshot).filter(
                    TemplateSnapshot.id == item['snapshotId']
                ).first()
                if snapshot:
                    self.db_session.delete(snapshot)
                    deleted_count += 1
            
            self.db_session.commit()
        
        return {
            'toDelete': to_delete,
            'deleted': deleted_count if not dry_run else None
        }
    
    def _is_major_version(self, version):
        """判断是否为主版本"""
        try:
            parts = version.split('.')
            if len(parts) >= 2:
                return parts[1] == '0' and parts[2] == '0'
        except:
            pass
        return False
