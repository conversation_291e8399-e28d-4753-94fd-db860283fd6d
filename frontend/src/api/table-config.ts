import request, { type RequestConfig } from '@/utils/request'
import {
  type TableViewConfig,
  type CustomFieldDefinition,
  type ExportConfig,
  type ExtendedReviewItem
} from '@/types/table-config'

/**
 * 表格配置相关API
 */
class TableConfigApiService {
  private baseUrl = '/api/table-config'

  /**
   * 获取用户的表格配置列表
   */
  async getTableConfigs(config?: RequestConfig): Promise<TableViewConfig[]> {
    return request.get(`${this.baseUrl}/configs`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 保存表格配置
   */
  async saveTableConfig(tableConfig: TableViewConfig, config?: RequestConfig): Promise<TableViewConfig> {
    if (!tableConfig.id) {
      throw new Error('配置ID不能为空')
    }

    return request.post(`${this.baseUrl}/configs`, tableConfig, {
      showLoading: true,
      showSuccess: true,
      successMessage: '配置保存成功',
      ...config,
    })
  }

  /**
   * 删除表格配置
   */
  async deleteTableConfig(configId: string, config?: RequestConfig): Promise<void> {
    if (!configId?.trim()) {
      throw new Error('配置ID不能为空')
    }

    return request.delete(`${this.baseUrl}/configs/${configId}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '配置删除成功',
      ...config,
    })
  }

  /**
   * 获取自定义字段定义
   */
  async getCustomFields(config?: RequestConfig): Promise<CustomFieldDefinition[]> {
    return request.get(`${this.baseUrl}/custom-fields`, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 保存自定义字段定义
   */
  async saveCustomField(field: CustomFieldDefinition, config?: RequestConfig): Promise<CustomFieldDefinition> {
    if (!field.key?.trim()) {
      throw new Error('字段键不能为空')
    }

    return request.post(`${this.baseUrl}/custom-fields`, field, {
      showLoading: true,
      showSuccess: true,
      successMessage: '自定义字段保存成功',
      ...config,
    })
  }

  /**
   * 删除自定义字段定义
   */
  async deleteCustomField(fieldKey: string, config?: RequestConfig): Promise<void> {
    if (!fieldKey?.trim()) {
      throw new Error('字段键不能为空')
    }

    return request.delete(`${this.baseUrl}/custom-fields/${fieldKey}`, {
      showLoading: true,
      showSuccess: true,
      successMessage: '自定义字段删除成功',
      ...config,
    })
  }

  /**
   * 更新评审项的自定义字段
   */
  async updateCustomFields(
    reviewId: string, 
    itemId: string, 
    customFields: Record<string, any>,
    config?: RequestConfig
  ): Promise<void> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }
    if (!itemId?.trim()) {
      throw new Error('评审项ID不能为空')
    }

    return request.put(`${this.baseUrl}/reviews/${reviewId}/items/${itemId}/custom-fields`, {
      customFields
    }, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 导出评审数据
   */
  async exportReviewData(
    reviewId: string,
    exportConfig: ExportConfig,
    config?: RequestConfig
  ): Promise<Blob> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    const response = await request.post(`${this.baseUrl}/reviews/${reviewId}/export`, exportConfig, {
      showLoading: true,
      responseType: 'blob',
      ...config,
    })

    return response as Blob
  }

  /**
   * 批量更新评审项的自定义字段
   */
  async batchUpdateCustomFields(
    reviewId: string,
    updates: Array<{
      itemId: string
      customFields: Record<string, any>
    }>,
    config?: RequestConfig
  ): Promise<void> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }
    if (!Array.isArray(updates) || updates.length === 0) {
      throw new Error('更新数据不能为空')
    }

    return request.put(`${this.baseUrl}/reviews/${reviewId}/batch-update-custom-fields`, {
      updates
    }, {
      showLoading: true,
      showSuccess: true,
      successMessage: '批量更新成功',
      ...config,
    })
  }

  /**
   * 获取评审数据的统计信息（按自定义字段分组）
   */
  async getReviewStatistics(
    reviewId: string,
    groupByField?: string,
    config?: RequestConfig
  ): Promise<{
    total: number
    groups: Record<string, {
      count: number
      passed: number
      failed: number
      skipped: number
      pending: number
    }>
  }> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    const params = groupByField ? { groupBy: groupByField } : {}
    
    return request.get(`${this.baseUrl}/reviews/${reviewId}/statistics`, {
      params,
      showLoading: false,
      ...config,
    })
  }

  /**
   * 根据自定义字段搜索评审项
   */
  async searchReviewItems(
    reviewId: string,
    searchCriteria: {
      customFields?: Record<string, any>
      status?: string[]
      dateRange?: {
        start: string
        end: string
      }
    },
    config?: RequestConfig
  ): Promise<ExtendedReviewItem[]> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.post(`${this.baseUrl}/reviews/${reviewId}/search`, searchCriteria, {
      showLoading: false,
      ...config,
    })
  }

  /**
   * 生成缺陷报告
   */
  async generateDefectReport(
    reviewId: string,
    options: {
      includeOnlyFailed?: boolean
      customFieldsMapping?: Record<string, string>  // 自定义字段到缺陷字段的映射
      template?: string
    },
    config?: RequestConfig
  ): Promise<{
    defects: Array<{
      title: string
      description: string
      severity: string
      category: string
      customData: Record<string, any>
    }>
    summary: {
      total: number
      byCategory: Record<string, number>
      bySeverity: Record<string, number>
    }
  }> {
    if (!reviewId?.trim()) {
      throw new Error('评审ID不能为空')
    }

    return request.post(`${this.baseUrl}/reviews/${reviewId}/generate-defects`, options, {
      showLoading: true,
      showSuccess: true,
      successMessage: '缺陷报告生成成功',
      ...config,
    })
  }
}

// Create and export service instance
const tableConfigApi = new TableConfigApiService()

// Export individual methods for convenience
export const {
  getTableConfigs,
  saveTableConfig,
  deleteTableConfig,
  getCustomFields,
  saveCustomField,
  deleteCustomField,
  updateCustomFields,
  exportReviewData,
  batchUpdateCustomFields,
  getReviewStatistics,
  searchReviewItems,
  generateDefectReport
} = tableConfigApi

export default tableConfigApi

// Export utility functions
export function downloadBlob(blob: Blob, filename: string) {
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

export function formatExportFilename(reviewType: string, format: string): string {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
  return `checklist_review_${reviewType}_${timestamp}.${format}`
}
