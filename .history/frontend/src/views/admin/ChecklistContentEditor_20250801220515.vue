<template>
  <div class="checklist-content-editor">
    <div class="page-header">
      <h1>CheckList评审表内容编辑</h1>
      <p>管理检查单模板，创建和编辑不同类型的评审模板</p>
    </div>
    
    <div class="page-content">
      <TemplateList />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import TemplateList from '@/components/admin/TemplateList.vue'

// Set page title
onMounted(() => {
  document.title = 'CheckList评审表内容编辑 - Checklist Review System'
})
</script>

<style scoped>
.checklist-content-editor {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.page-header {
  background-color: #fff;
  padding: 30px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #303133;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.page-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
