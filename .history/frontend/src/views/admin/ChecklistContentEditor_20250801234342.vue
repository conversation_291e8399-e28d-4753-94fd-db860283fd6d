<template>
  <div class="checklist-content-editor">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">📝</div>
          <div class="header-text">
            <h1>CheckList评审表内容编辑</h1>
            <p>创建和编辑评审模板，定义检查项内容和评审流程</p>
          </div>
        </div>

      </div>
    </div>

    <div class="page-content">
      <TemplateList />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import TemplateList from '@/components/admin/TemplateList.vue'

// Set page title
onMounted(() => {
  document.title = 'CheckList评审表内容编辑 - Checklist Review System'
})
</script>

<style scoped>
.checklist-content-editor {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.page-header {
  background: #fff;
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-content {
  padding: 32px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text h1 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}



.page-content {
  background: #fff;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
</style>
