<template>
  <div class="checklist-item-config">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">⚙️</div>
          <div class="header-text">
            <h1>CheckList评审项配置</h1>
            <p>配置检查项的启用状态、分类标签和详细属性</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="quick-stats">
            <div class="stat-badge enabled">
              <span class="stat-dot"></span>
              <span>启用项目</span>
            </div>
            <div class="stat-badge disabled">
              <span class="stat-dot"></span>
              <span>禁用项目</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="page-content">
      <ItemConfig />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import ItemConfig from '@/components/admin/ItemConfig.vue'

// Set page title
onMounted(() => {
  document.title = 'CheckList评审项配置 - Checklist Review System'
})
</script>

<style scoped>
.checklist-item-config {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.page-header {
  background-color: #fff;
  padding: 30px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #303133;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.page-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
