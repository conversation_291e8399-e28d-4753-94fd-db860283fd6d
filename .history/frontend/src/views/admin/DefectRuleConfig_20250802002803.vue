<template>
  <div class="defect-rule-config">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">🐛</div>
          <div class="header-text">
            <h1>缺陷生成规则配置</h1>
            <p>配置检查项不通过时自动生成缺陷的规则和模板</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            v-if="selectedTemplateId"
            @click="saveConfiguration"
            type="success"
          >
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
          <el-button type="primary" @click="showAddRuleDialog = true">
            <el-icon><Plus /></el-icon>
            添加缺陷规则
          </el-button>
        </div>
      </div>
    </div>

    <div class="page-content">
      <!-- 模板选择 -->
      <div class="template-selector">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>选择模板</span>
            </div>
          </template>
          
          <el-select
            v-model="selectedTemplateId"
            @change="handleTemplateChange"
            placeholder="选择要配置的模板"
            style="width: 300px"
          >
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="`${template.name} (${template.type})`"
              :value="template.id"
            />
          </el-select>
        </el-card>
      </div>

      <!-- 缺陷规则列表 -->
      <div v-if="selectedTemplateId" class="rules-list">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>缺陷生成规则</span>
            </div>
          </template>

          <div class="rules-container">
            <div
              v-for="rule in defectRules"
              :key="rule.id"
              :class="['rule-item', { disabled: !rule.enabled }]"
            >
              <div class="rule-header">
                <div class="rule-info">
                  <div class="rule-title">
                    <h3>{{ rule.name }}</h3>
                    <el-switch
                      v-model="rule.enabled"
                      @change="handleRuleToggle(rule)"
                    />
                  </div>
                  <p class="rule-description">{{ rule.description }}</p>
                  <div class="rule-meta">
                    <el-tag
                      v-for="status in rule.trigger.status"
                      :key="status"
                      size="small"
                      type="warning"
                    >
                      触发状态: {{ getStatusText(status) }}
                    </el-tag>
                    <el-tag size="small" type="info">
                      {{ rule.options.autoGenerate ? '自动生成' : '手动确认' }}
                    </el-tag>
                  </div>
                </div>
                <div class="rule-actions">
                  <el-button @click="testRule(rule)">测试</el-button>
                  <el-button @click="editRule(rule)">编辑</el-button>
                  <el-button @click="duplicateRule(rule)">复制</el-button>
                  <el-button type="danger" @click="deleteRule(rule.id)">删除</el-button>
                </div>
              </div>

              <!-- 规则详情 -->
              <div class="rule-details">
                <el-collapse>
                  <el-collapse-item title="模板配置" name="template">
                    <div class="template-config">
                      <el-descriptions :column="1" border>
                        <el-descriptions-item label="标题模板">
                          <code>{{ rule.template.titleTemplate }}</code>
                        </el-descriptions-item>
                        <el-descriptions-item label="描述模板">
                          <div class="description-template">
                            <pre>{{ rule.template.descriptionTemplate }}</pre>
                          </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="严重程度映射">
                          <div class="mapping-list">
                            <el-tag
                              v-for="[key, value] in Object.entries(rule.template.severityMapping)"
                              :key="key"
                              size="small"
                              class="mapping-tag"
                            >
                              {{ key }} → {{ value }}
                            </el-tag>
                          </div>
                        </el-descriptions-item>
                        <el-descriptions-item label="分类映射">
                          <div class="mapping-list">
                            <el-tag
                              v-for="[key, value] in Object.entries(rule.template.categoryMapping)"
                              :key="key"
                              size="small"
                              class="mapping-tag"
                            >
                              {{ key }} → {{ value }}
                            </el-tag>
                          </div>
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </el-collapse-item>
                  
                  <el-collapse-item title="触发条件" name="conditions">
                    <div class="conditions-config">
                      <div v-if="rule.trigger.conditions && rule.trigger.conditions.length > 0">
                        <div
                          v-for="(condition, index) in rule.trigger.conditions"
                          :key="index"
                          class="condition-item"
                        >
                          <el-tag size="small">
                            {{ condition.field }} {{ condition.operator }} {{ condition.value }}
                          </el-tag>
                          <el-tag v-if="condition.logicOperator" size="small" type="info">
                            {{ condition.logicOperator }}
                          </el-tag>
                        </div>
                      </div>
                      <div v-else class="no-conditions">
                        <span>无额外条件</span>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加/编辑规则对话框 -->
    <el-dialog
      v-model="showAddRuleDialog"
      :title="editingRule ? '编辑缺陷规则' : '添加缺陷规则'"
      width="800px"
      :before-close="cancelRuleEdit"
    >
      <el-form :model="ruleForm" :rules="ruleRules" ref="ruleFormRef" label-width="120px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="规则名称" prop="name">
              <el-input v-model="ruleForm.name" placeholder="请输入规则名称" />
            </el-form-item>
            <el-form-item label="规则描述" prop="description">
              <el-input
                v-model="ruleForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入规则描述"
              />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="ruleForm.enabled" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="触发条件" name="trigger">
            <el-form-item label="触发状态" prop="triggerStatus">
              <el-select
                v-model="ruleForm.trigger.status"
                multiple
                placeholder="选择触发缺陷生成的状态"
              >
                <el-option label="不通过" value="FAIL" />
                <el-option label="条件通过" value="CONDITIONAL_PASS" />
                <el-option label="部分通过" value="PARTIAL_PASS" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="额外条件">
              <div class="conditions-editor">
                <div
                  v-for="(condition, index) in ruleForm.trigger.conditions"
                  :key="index"
                  class="condition-row"
                >
                  <el-select v-model="condition.field" placeholder="字段">
                    <el-option label="分类" value="category" />
                    <el-option label="备注" value="comment" />
                    <el-option label="是否必填" value="required" />
                  </el-select>
                  <el-select v-model="condition.operator" placeholder="操作符">
                    <el-option label="等于" value="equals" />
                    <el-option label="包含" value="contains" />
                    <el-option label="存在" value="exists" />
                    <el-option label="不存在" value="notExists" />
                  </el-select>
                  <el-input v-model="condition.value" placeholder="值" />
                  <el-select v-model="condition.logicOperator" placeholder="逻辑">
                    <el-option label="AND" value="AND" />
                    <el-option label="OR" value="OR" />
                  </el-select>
                  <el-button type="danger" @click="removeCondition(index)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
                <el-button @click="addCondition">
                  <el-icon><Plus /></el-icon>
                  添加条件
                </el-button>
              </div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="缺陷模板" name="template">
            <el-form-item label="标题模板" prop="titleTemplate">
              <el-input
                v-model="ruleForm.template.titleTemplate"
                placeholder="支持变量: ${content}, ${category}, ${customFields.xxx}"
              />
            </el-form-item>
            <el-form-item label="描述模板" prop="descriptionTemplate">
              <el-input
                v-model="ruleForm.template.descriptionTemplate"
                type="textarea"
                :rows="6"
                placeholder="支持变量替换的描述模板"
              />
            </el-form-item>
            
            <el-form-item label="严重程度映射">
              <div class="mapping-editor">
                <div
                  v-for="[key, value] in Object.entries(ruleForm.template.severityMapping)"
                  :key="key"
                  class="mapping-row"
                >
                  <el-input v-model="key" placeholder="分类" readonly />
                  <span>→</span>
                  <el-select v-model="ruleForm.template.severityMapping[key]">
                    <el-option label="高" value="high" />
                    <el-option label="中" value="medium" />
                    <el-option label="低" value="low" />
                  </el-select>
                </div>
              </div>
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="生成选项" name="options">
            <el-form-item label="自动生成">
              <el-switch v-model="ruleForm.options.autoGenerate" />
              <div class="form-help">开启后，满足条件时自动生成缺陷</div>
            </el-form-item>
            <el-form-item label="需要确认">
              <el-switch v-model="ruleForm.options.requireConfirmation" />
              <div class="form-help">生成缺陷前需要用户确认</div>
            </el-form-item>
            <el-form-item label="批量生成">
              <el-switch v-model="ruleForm.options.batchGenerate" />
              <div class="form-help">支持批量生成多个缺陷</div>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelRuleEdit">取消</el-button>
        <el-button type="primary" @click="saveRule">保存</el-button>
      </template>
    </el-dialog>

    <!-- 规则测试对话框 -->
    <el-dialog
      v-model="showTestDialog"
      title="测试缺陷规则"
      width="600px"
    >
      <div v-if="testingRule" class="rule-test">
        <h4>测试数据</h4>
        <el-form :model="testData" label-width="100px">
          <el-form-item label="检查内容">
            <el-input v-model="testData.content" />
          </el-form-item>
          <el-form-item label="分类">
            <el-input v-model="testData.category" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="testData.status">
              <el-option label="不通过" value="FAIL" />
              <el-option label="条件通过" value="CONDITIONAL_PASS" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="testData.comment" />
          </el-form-item>
        </el-form>
        
        <el-button type="primary" @click="runTest">运行测试</el-button>
        
        <div v-if="testResult" class="test-result">
          <h4>生成结果</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="缺陷标题">
              {{ testResult.title }}
            </el-descriptions-item>
            <el-descriptions-item label="缺陷描述">
              <pre>{{ testResult.description }}</pre>
            </el-descriptions-item>
            <el-descriptions-item label="严重程度">
              {{ testResult.severity }}
            </el-descriptions-item>
            <el-descriptions-item label="分类">
              {{ testResult.category }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showTestDialog = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Plus, Delete, Check } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTemplates, type ChecklistTemplate } from '@/api/template'
import {
  type DefectRule,
  type DefectCondition,
  type GeneratedDefect,
  DEFAULT_DEFECT_RULES,
  replaceTemplateVariables
} from '@/types/defect-config'
import {
  getTemplateDefectRules,
  saveTemplateDefectRules,
  validateDefectRule
} from '@/api/admin-config'
import { type ExtendedReviewItem } from '@/types/table-config'
import { ReviewItemStatus } from '@/api/review'

// Reactive data
const templates = ref<ChecklistTemplate[]>([])
const selectedTemplateId = ref('')
const defectRules = ref<DefectRule[]>([])
const loading = ref(false)

const showAddRuleDialog = ref(false)
const showTestDialog = ref(false)
const editingRule = ref<DefectRule | null>(null)
const testingRule = ref<DefectRule | null>(null)
const activeTab = ref('basic')

const ruleForm = ref({
  name: '',
  description: '',
  enabled: true,
  trigger: {
    status: [] as string[],
    conditions: [] as DefectCondition[]
  },
  template: {
    titleTemplate: '',
    descriptionTemplate: '',
    severityMapping: {
      '安全': 'high',
      '功能': 'medium',
      '性能': 'medium',
      '界面': 'low',
      'default': 'medium'
    },
    categoryMapping: {
      '安全检查': 'security',
      '功能检查': 'functional',
      '性能检查': 'performance',
      '界面检查': 'ui',
      'default': 'general'
    },
    customFieldMapping: {}
  },
  options: {
    autoGenerate: false,
    requireConfirmation: true,
    batchGenerate: true
  }
})

const testData = ref({
  content: '示例检查项内容',
  category: '功能',
  status: 'FAIL',
  comment: '测试不通过原因'
})

const testResult = ref<GeneratedDefect | null>(null)
const ruleFormRef = ref()

// Validation rules
const ruleRules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入规则描述', trigger: 'blur' }],
  triggerStatus: [{ required: true, message: '请选择触发状态', trigger: 'change' }],
  titleTemplate: [{ required: true, message: '请输入标题模板', trigger: 'blur' }],
  descriptionTemplate: [{ required: true, message: '请输入描述模板', trigger: 'blur' }]
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})

// Methods
const loadTemplates = async () => {
  try {
    loading.value = true
    templates.value = await getTemplates()
  } catch (error: any) {
    ElMessage.error(error.message || '加载模板失败')
  } finally {
    loading.value = false
  }
}

const handleTemplateChange = async (templateId: string) => {
  try {
    const rules = await getTemplateDefectRules(templateId)
    defectRules.value = rules.length > 0 ? rules : [...DEFAULT_DEFECT_RULES]
  } catch (error: any) {
    ElMessage.error(error.message || '加载缺陷规则失败')
    // 如果加载失败，使用默认配置
    defectRules.value = [...DEFAULT_DEFECT_RULES]
  }
}

const handleRuleToggle = (rule: DefectRule) => {
  ElMessage.success(`规则已${rule.enabled ? '启用' : '禁用'}`)
}

const editRule = (rule: DefectRule) => {
  editingRule.value = rule
  ruleForm.value = {
    name: rule.name,
    description: rule.description,
    enabled: rule.enabled,
    trigger: {
      status: [...rule.trigger.status],
      conditions: rule.trigger.conditions ? [...rule.trigger.conditions] : []
    },
    template: {
      titleTemplate: rule.template.titleTemplate,
      descriptionTemplate: rule.template.descriptionTemplate,
      severityMapping: { ...rule.template.severityMapping },
      categoryMapping: { ...rule.template.categoryMapping },
      customFieldMapping: { ...rule.template.customFieldMapping }
    },
    options: { ...rule.options }
  }
  showAddRuleDialog.value = true
}

const duplicateRule = (rule: DefectRule) => {
  const newRule: DefectRule = {
    ...rule,
    id: `rule_${Date.now()}`,
    name: `${rule.name} - 副本`
  }
  defectRules.value.push(newRule)
  ElMessage.success('规则已复制')
}

const deleteRule = async (ruleId: string) => {
  try {
    await ElMessageBox.confirm('确认删除此规则？', '确认删除', {
      type: 'warning'
    })
    
    const index = defectRules.value.findIndex(r => r.id === ruleId)
    if (index > -1) {
      defectRules.value.splice(index, 1)
      ElMessage.success('规则已删除')
    }
  } catch {
    // User cancelled
  }
}

const testRule = (rule: DefectRule) => {
  testingRule.value = rule
  testResult.value = null
  showTestDialog.value = true
}

const runTest = () => {
  if (!testingRule.value) return
  
  // 创建测试用的检查项数据
  const testItem: ExtendedReviewItem = {
    itemId: 'test-item',
    sequence: 1,
    content: testData.value.content,
    category: testData.value.category,
    status: testData.value.status as ReviewItemStatus,
    comment: testData.value.comment,
    required: false,
    reviewHistory: []
  }
  
  // 生成缺陷
  const context = {
    currentUser: 'test-user',
    currentTime: new Date().toISOString()
  }
  
  const rule = testingRule.value
  const title = replaceTemplateVariables(rule.template.titleTemplate, testItem, context)
  const description = replaceTemplateVariables(rule.template.descriptionTemplate, testItem, context)
  const severity = rule.template.severityMapping[testItem.category] || rule.template.severityMapping.default || 'medium'
  const category = rule.template.categoryMapping[testItem.category] || rule.template.categoryMapping.default || 'general'
  
  testResult.value = {
    id: 'test-defect',
    title,
    description,
    severity,
    category,
    sourceItemId: testItem.itemId,
    sourceContent: testItem.content,
    customData: {},
    createdTime: new Date().toISOString(),
    status: 'draft'
  }
}

const addCondition = () => {
  ruleForm.value.trigger.conditions.push({
    field: '',
    operator: 'equals',
    value: '',
    logicOperator: 'AND'
  })
}

const removeCondition = (index: number) => {
  ruleForm.value.trigger.conditions.splice(index, 1)
}

const saveRule = async () => {
  try {
    await ruleFormRef.value.validate()
    
    const ruleData: DefectRule = {
      id: editingRule.value?.id || `rule_${Date.now()}`,
      name: ruleForm.value.name,
      description: ruleForm.value.description,
      enabled: ruleForm.value.enabled,
      trigger: {
        status: ruleForm.value.trigger.status as any,
        conditions: ruleForm.value.trigger.conditions.length > 0 
          ? ruleForm.value.trigger.conditions 
          : undefined
      },
      template: ruleForm.value.template,
      options: ruleForm.value.options
    }
    
    if (editingRule.value) {
      // 编辑现有规则
      const index = defectRules.value.findIndex(r => r.id === editingRule.value!.id)
      if (index > -1) {
        defectRules.value[index] = ruleData
      }
    } else {
      // 添加新规则
      defectRules.value.push(ruleData)
    }
    
    showAddRuleDialog.value = false
    ElMessage.success(editingRule.value ? '规则已更新' : '规则已添加')
  } catch {
    // Validation failed
  }
}

const cancelRuleEdit = () => {
  showAddRuleDialog.value = false
  editingRule.value = null
  resetRuleForm()
}

const resetRuleForm = () => {
  ruleForm.value = {
    name: '',
    description: '',
    enabled: true,
    trigger: {
      status: [],
      conditions: []
    },
    template: {
      titleTemplate: '',
      descriptionTemplate: '',
      severityMapping: {
        '安全': 'high',
        '功能': 'medium',
        '性能': 'medium',
        '界面': 'low',
        'default': 'medium'
      },
      categoryMapping: {
        '安全检查': 'security',
        '功能检查': 'functional',
        '性能检查': 'performance',
        '界面检查': 'ui',
        'default': 'general'
      },
      customFieldMapping: {}
    },
    options: {
      autoGenerate: false,
      requireConfirmation: true,
      batchGenerate: true
    }
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    FAIL: '不通过',
    CONDITIONAL_PASS: '条件通过',
    PARTIAL_PASS: '部分通过'
  }
  return statusMap[status] || status
}

// 保存配置到后台
const saveConfiguration = async () => {
  if (!selectedTemplateId.value) {
    ElMessage.warning('请先选择模板')
    return
  }

  // 验证配置
  const errors: string[] = []
  defectRules.value.forEach((rule, index) => {
    const ruleErrors = validateDefectRule(rule)
    if (ruleErrors.length > 0) {
      errors.push(`规则 ${index + 1}: ${ruleErrors.join(', ')}`)
    }
  })

  if (errors.length > 0) {
    ElMessage.error(`配置验证失败: ${errors.join('; ')}`)
    return
  }

  try {
    await saveTemplateDefectRules(selectedTemplateId.value, defectRules.value)
    ElMessage.success('配置已保存')
  } catch (error: any) {
    ElMessage.error(error.message || '保存配置失败')
  }
}
</script>

<style scoped>
.defect-rule-config {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.page-header {
  background: #fff;
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-content {
  padding: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
}

.header-text h1 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.page-content {
  .template-selector {
    margin-bottom: 24px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .rules-container {
    .rule-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 16px;
      overflow: hidden;
      transition: all 0.3s;
    }

    .rule-item:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .rule-item.disabled {
      opacity: 0.6;
    }

    .rule-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 16px;
      background: #fafbfc;
      border-bottom: 1px solid #e4e7ed;
    }

    .rule-title {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;
    }

    .rule-title h3 {
      margin: 0;
      color: #303133;
    }

    .rule-description {
      margin: 0 0 12px 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }

    .rule-meta {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .rule-details {
      padding: 16px;
    }

    .template-config {
      .description-template {
        max-height: 200px;
        overflow-y: auto;
        background: #f8f9fa;
        padding: 12px;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
        line-height: 1.4;
      }

      .mapping-list {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      .mapping-tag {
        font-family: monospace;
        font-size: 12px;
      }
    }

    .conditions-config {
      .condition-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
      }

      .no-conditions {
        color: #909399;
        font-style: italic;
      }
    }
  }
}

.conditions-editor {
  .condition-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    align-items: center;
  }

  .condition-row .el-select,
  .condition-row .el-input {
    flex: 1;
  }
}

.mapping-editor {
  .mapping-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
  }

  .mapping-row .el-input {
    flex: 1;
  }

  .mapping-row .el-select {
    flex: 1;
  }
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.rule-test {
  .test-result {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #e4e7ed;
  }

  .test-result pre {
    white-space: pre-wrap;
    font-size: 12px;
    line-height: 1.4;
    max-height: 200px;
    overflow-y: auto;
  }
}
</style>
