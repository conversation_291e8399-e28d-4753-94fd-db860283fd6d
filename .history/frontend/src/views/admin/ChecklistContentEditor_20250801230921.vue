<template>
  <div class="checklist-content-editor">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">📝</div>
          <div class="header-text">
            <h1>CheckList评审表内容编辑</h1>
            <p>创建和编辑评审模板，定义检查项内容和评审流程</p>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ templateCount }}</div>
            <div class="stat-label">模板总数</div>
          </div>
        </div>
      </div>
    </div>

    <div class="page-content">
      <TemplateList @template-count-change="handleTemplateCountChange" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import TemplateList from '@/components/admin/TemplateList.vue'

const templateCount = ref(0)

const handleTemplateCountChange = (count: number) => {
  templateCount.value = count
}

// Set page title
onMounted(() => {
  document.title = 'CheckList评审表内容编辑 - Checklist Review System'
})
</script>

<style scoped>
.checklist-content-editor {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.page-header {
  background-color: #fff;
  padding: 30px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #303133;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.page-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
