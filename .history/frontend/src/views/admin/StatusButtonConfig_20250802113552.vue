<template>
  <div class="status-button-config">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">🔘</div>
          <div class="header-text">
            <h1>状态按钮配置</h1>
            <p>配置检查项的状态按钮和对应的API接口</p>
          </div>
        </div>
        <div class="header-actions">
          <el-button
            v-if="selectedTemplateId"
            @click="saveConfiguration"
            type="success"
          >
            <el-icon><Check /></el-icon>
            保存配置
          </el-button>
          <el-button type="primary" @click="showAddButtonDialog = true">
            <el-icon><Plus /></el-icon>
            添加状态按钮
          </el-button>
        </div>
      </div>
    </div>

    <div class="page-content">
      <!-- 模板选择 -->
      <div class="template-selector">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>选择模板</span>
            </div>
          </template>
          
          <el-select
            v-model="selectedTemplateId"
            @change="handleTemplateChange"
            placeholder="选择要配置的模板"
            style="width: 300px"
          >
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="`${template.name} (${template.type})`"
              :value="template.id"
            />
          </el-select>
        </el-card>
      </div>

      <!-- 按钮组配置 -->
      <div v-if="selectedTemplateId" class="button-groups">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span>状态按钮组</span>
              <el-button @click="showAddGroupDialog = true">
                <el-icon><Plus /></el-icon>
                添加按钮组
              </el-button>
            </div>
          </template>

          <div class="groups-list">
            <div
              v-for="group in buttonGroups"
              :key="group.id"
              class="group-item"
            >
              <div class="group-header">
                <div class="group-info">
                  <h3>{{ group.name }}</h3>
                  <p>{{ group.description }}</p>
                  <el-tag size="small">{{ group.buttons.length }} 个按钮</el-tag>
                </div>
                <div class="group-actions">
                  <el-button @click="editGroup(group)">编辑</el-button>
                  <el-button @click="duplicateGroup(group)">复制</el-button>
                  <el-button type="danger" @click="deleteGroup(group.id)">删除</el-button>
                </div>
              </div>

              <!-- 按钮列表 -->
              <div class="buttons-list">
                <draggable
                  v-model="group.buttons"
                  item-key="id"
                  handle=".drag-handle"
                  @end="handleButtonReorder"
                  class="draggable-buttons"
                >
                  <template #item="{ element: button, index }">
                    <div class="button-item">
                      <div class="button-info">
                        <el-icon class="drag-handle"><Rank /></el-icon>
                        <el-button
                          :type="button.type"
                          size="small"
                          disabled
                        >
                          <el-icon v-if="button.icon">
                            <component :is="button.icon" />
                          </el-icon>
                          {{ button.label }}
                        </el-button>
                        <div class="button-meta">
                          <el-tag size="small">{{ button.status }}</el-tag>
                          <span class="api-endpoint">{{ button.action.apiEndpoint }}</span>
                        </div>
                      </div>
                      <div class="button-actions">
                        <el-button size="small" @click="editButton(group, button, index)">
                          <el-icon><Edit /></el-icon>
                        </el-button>
                        <el-button size="small" type="danger" @click="deleteButton(group, index)">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </div>
                    </div>
                  </template>
                </draggable>
                
                <el-button
                  class="add-button-btn"
                  @click="addButtonToGroup(group)"
                  dashed
                  style="width: 100%; margin-top: 12px"
                >
                  <el-icon><Plus /></el-icon>
                  添加按钮
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加/编辑按钮组对话框 -->
    <el-dialog
      v-model="showAddGroupDialog"
      :title="editingGroup ? '编辑按钮组' : '添加按钮组'"
      width="500px"
    >
      <el-form :model="groupForm" :rules="groupRules" ref="groupFormRef" label-width="100px">
        <el-form-item label="组名称" prop="name">
          <el-input v-model="groupForm.name" placeholder="请输入按钮组名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="groupForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入按钮组描述"
          />
        </el-form-item>
        <el-form-item label="布局方式">
          <el-radio-group v-model="groupForm.layout">
            <el-radio value="horizontal">水平排列</el-radio>
            <el-radio value="vertical">垂直排列</el-radio>
            <el-radio value="grid">网格排列</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelGroupEdit">取消</el-button>
        <el-button type="primary" @click="saveGroup">保存</el-button>
      </template>
    </el-dialog>

    <!-- 添加/编辑按钮对话框 -->
    <el-dialog
      v-model="showAddButtonDialog"
      :title="editingButton ? '编辑状态按钮' : '添加状态按钮'"
      width="700px"
    >
      <el-form :model="buttonForm" :rules="buttonRules" ref="buttonFormRef" label-width="120px">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="按钮标签" prop="label">
              <el-input v-model="buttonForm.label" placeholder="请输入按钮显示文本" />
            </el-form-item>
            <el-form-item label="状态值" prop="status">
              <el-input v-model="buttonForm.status" placeholder="请输入状态值，如 PASS, FAIL" />
            </el-form-item>
            <el-form-item label="按钮类型" prop="type">
              <el-select v-model="buttonForm.type">
                <el-option label="主要" value="primary" />
                <el-option label="成功" value="success" />
                <el-option label="警告" value="warning" />
                <el-option label="危险" value="danger" />
                <el-option label="信息" value="info" />
              </el-select>
            </el-form-item>
            <el-form-item label="图标">
              <el-input v-model="buttonForm.icon" placeholder="请输入图标名称" />
            </el-form-item>
            <el-form-item label="排序">
              <el-input-number v-model="buttonForm.order" :min="1" />
            </el-form-item>
            <el-form-item label="启用状态">
              <el-switch v-model="buttonForm.enabled" />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="API配置" name="api">
            <el-form-item label="API接口" prop="apiEndpoint">
              <el-input
                v-model="buttonForm.action.apiEndpoint"
                placeholder="请输入API接口地址，如 /api/review/items/{itemId}/status"
              />
            </el-form-item>
            <el-form-item label="请求方法" prop="method">
              <el-select v-model="buttonForm.action.method">
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="PATCH" value="PATCH" />
              </el-select>
            </el-form-item>
            <el-form-item label="需要备注">
              <el-switch v-model="buttonForm.action.requireComment" />
            </el-form-item>
            <el-form-item label="确认消息">
              <el-input v-model="buttonForm.action.confirmMessage" placeholder="点击按钮时的确认消息" />
            </el-form-item>
            <el-form-item label="成功消息">
              <el-input v-model="buttonForm.action.successMessage" placeholder="操作成功时的提示消息" />
            </el-form-item>
            <el-form-item label="请求参数模板">
              <el-input
                v-model="payloadTemplateText"
                type="textarea"
                :rows="6"
                placeholder="JSON格式的请求参数模板，支持变量替换"
              />
            </el-form-item>
          </el-tab-pane>

          <el-tab-pane label="显示条件" name="conditions">
            <el-form-item label="当前状态条件">
              <el-select
                v-model="buttonForm.displayConditions.currentStatus"
                multiple
                placeholder="选择可显示此按钮的当前状态"
              >
                <el-option label="待处理" value="PENDING" />
                <el-option label="通过" value="PASS" />
                <el-option label="不通过" value="FAIL" />
                <el-option label="跳过" value="SKIP" />
              </el-select>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      
      <template #footer>
        <el-button @click="cancelButtonEdit">取消</el-button>
        <el-button type="primary" @click="saveButton">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Plus, Rank, Edit, Delete, Check } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import draggable from 'vuedraggable'
import { getTemplates, type ChecklistTemplate } from '@/api/template'
import {
  type StatusButtonGroup,
  type CustomStatusButton,
  DEFAULT_BUTTON_GROUP
} from '@/types/defect-config'
import {
  getTemplateButtonGroups,
  saveTemplateButtonGroups,
  validateButtonGroup
} from '@/api/admin-config'

// Reactive data
const templates = ref<ChecklistTemplate[]>([])
const selectedTemplateId = ref('')
const buttonGroups = ref<StatusButtonGroup[]>([])
const loading = ref(false)

const showAddGroupDialog = ref(false)
const showAddButtonDialog = ref(false)
const editingGroup = ref<StatusButtonGroup | null>(null)
const editingButton = ref<CustomStatusButton | null>(null)
const editingButtonIndex = ref(-1)
const editingButtonGroup = ref<StatusButtonGroup | null>(null)
const activeTab = ref('basic')

const groupForm = ref({
  name: '',
  description: '',
  layout: 'horizontal'
})

const buttonForm = ref({
  label: '',
  status: '',
  type: 'primary',
  icon: '',
  order: 1,
  enabled: true,
  action: {
    apiEndpoint: '',
    method: 'PUT',
    requireComment: false,
    confirmMessage: '',
    successMessage: '',
    payloadTemplate: {}
  },
  displayConditions: {
    currentStatus: []
  }
})

const payloadTemplateText = ref('')
const groupFormRef = ref()
const buttonFormRef = ref()

// Validation rules
const groupRules = {
  name: [{ required: true, message: '请输入按钮组名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入按钮组描述', trigger: 'blur' }]
}

const buttonRules = {
  label: [{ required: true, message: '请输入按钮标签', trigger: 'blur' }],
  status: [{ required: true, message: '请输入状态值', trigger: 'blur' }],
  type: [{ required: true, message: '请选择按钮类型', trigger: 'change' }],
  apiEndpoint: [{ required: true, message: '请输入API接口地址', trigger: 'blur' }],
  method: [{ required: true, message: '请选择请求方法', trigger: 'change' }]
}

// Lifecycle
onMounted(() => {
  loadTemplates()
})

// Methods
const loadTemplates = async () => {
  try {
    loading.value = true
    const response = await getTemplates()
    templates.value = response.templates
  } catch (error: any) {
    ElMessage.error(error.message || '加载模板失败')
  } finally {
    loading.value = false
  }
}

const handleTemplateChange = async (templateId: string) => {
  // 加载该模板的按钮组配置
  try {
    const groups = await getTemplateButtonGroups(templateId)
    buttonGroups.value = groups.length > 0 ? groups : [DEFAULT_BUTTON_GROUP]
  } catch (error: any) {
    ElMessage.error(error.message || '加载按钮组配置失败')
    // 如果加载失败，使用默认配置
    buttonGroups.value = [DEFAULT_BUTTON_GROUP]
  }
}

const editGroup = (group: StatusButtonGroup) => {
  editingGroup.value = group
  groupForm.value = {
    name: group.name,
    description: group.description,
    layout: group.layout
  }
  showAddGroupDialog.value = true
}

const duplicateGroup = (group: StatusButtonGroup) => {
  const newGroup: StatusButtonGroup = {
    ...group,
    id: `group_${Date.now()}`,
    name: `${group.name} - 副本`,
    buttons: group.buttons.map(btn => ({
      ...btn,
      id: `btn_${Date.now()}_${Math.random()}`
    }))
  }
  buttonGroups.value.push(newGroup)
  ElMessage.success('按钮组已复制')
}

const deleteGroup = async (groupId: string) => {
  try {
    await ElMessageBox.confirm('确认删除此按钮组？', '确认删除', {
      type: 'warning'
    })
    
    const index = buttonGroups.value.findIndex(g => g.id === groupId)
    if (index > -1) {
      buttonGroups.value.splice(index, 1)
      ElMessage.success('按钮组已删除')
    }
  } catch {
    // User cancelled
  }
}

const addButtonToGroup = (group: StatusButtonGroup) => {
  editingButtonGroup.value = group
  resetButtonForm()
  showAddButtonDialog.value = true
}

const editButton = (group: StatusButtonGroup, button: CustomStatusButton, index: number) => {
  editingButtonGroup.value = group
  editingButton.value = button
  editingButtonIndex.value = index
  
  buttonForm.value = {
    label: button.label,
    status: button.status as string,
    type: button.type,
    icon: button.icon || '',
    order: button.order,
    enabled: button.enabled,
    action: {
      ...button.action,
      confirmMessage: button.action.confirmMessage || '',
      successMessage: button.action.successMessage || ''
    },
    displayConditions: {
      currentStatus: button.displayConditions?.currentStatus || []
    }
  }
  
  payloadTemplateText.value = JSON.stringify(button.action.payloadTemplate, null, 2)
  showAddButtonDialog.value = true
}

const deleteButton = async (group: StatusButtonGroup, index: number) => {
  try {
    await ElMessageBox.confirm('确认删除此按钮？', '确认删除', {
      type: 'warning'
    })
    
    group.buttons.splice(index, 1)
    ElMessage.success('按钮已删除')
  } catch {
    // User cancelled
  }
}

const saveGroup = async () => {
  try {
    await groupFormRef.value.validate()
    
    if (editingGroup.value) {
      // 编辑现有组
      Object.assign(editingGroup.value, groupForm.value)
    } else {
      // 添加新组
      const newGroup: StatusButtonGroup = {
        id: `group_${Date.now()}`,
        ...groupForm.value,
        buttons: [],
        defaultButtons: []
      }
      buttonGroups.value.push(newGroup)
    }
    
    showAddGroupDialog.value = false
    ElMessage.success(editingGroup.value ? '按钮组已更新' : '按钮组已添加')
  } catch {
    // Validation failed
  }
}

const saveButton = async () => {
  try {
    await buttonFormRef.value.validate()
    
    // 解析请求参数模板
    let payloadTemplate = {}
    try {
      payloadTemplate = JSON.parse(payloadTemplateText.value || '{}')
    } catch {
      ElMessage.error('请求参数模板格式错误')
      return
    }
    
    const buttonData: CustomStatusButton = {
      id: editingButton.value?.id || `btn_${Date.now()}`,
      label: buttonForm.value.label,
      status: buttonForm.value.status,
      type: buttonForm.value.type as any,
      icon: buttonForm.value.icon || undefined,
      order: buttonForm.value.order,
      enabled: buttonForm.value.enabled,
      action: {
        ...buttonForm.value.action,
        payloadTemplate
      },
      displayConditions: buttonForm.value.displayConditions.currentStatus.length > 0 
        ? { currentStatus: buttonForm.value.displayConditions.currentStatus as any }
        : undefined
    }
    
    if (editingButton.value && editingButtonGroup.value) {
      // 编辑现有按钮
      editingButtonGroup.value.buttons[editingButtonIndex.value] = buttonData
    } else if (editingButtonGroup.value) {
      // 添加新按钮
      editingButtonGroup.value.buttons.push(buttonData)
    }
    
    showAddButtonDialog.value = false
    ElMessage.success(editingButton.value ? '按钮已更新' : '按钮已添加')
  } catch {
    // Validation failed
  }
}

const cancelGroupEdit = () => {
  showAddGroupDialog.value = false
  editingGroup.value = null
  groupForm.value = {
    name: '',
    description: '',
    layout: 'horizontal'
  }
}

const cancelButtonEdit = () => {
  showAddButtonDialog.value = false
  resetButtonForm()
}

const resetButtonForm = () => {
  editingButton.value = null
  editingButtonIndex.value = -1
  editingButtonGroup.value = null
  buttonForm.value = {
    label: '',
    status: '',
    type: 'primary',
    icon: '',
    order: 1,
    enabled: true,
    action: {
      apiEndpoint: '',
      method: 'PUT',
      requireComment: false,
      confirmMessage: '',
      successMessage: '',
      payloadTemplate: {}
    },
    displayConditions: {
      currentStatus: []
    }
  }
  payloadTemplateText.value = ''
}

const handleButtonReorder = () => {
  // 更新按钮顺序
  buttonGroups.value.forEach(group => {
    group.buttons.forEach((button, index) => {
      button.order = index + 1
    })
  })
}

// 保存配置到后台
const saveConfiguration = async () => {
  if (!selectedTemplateId.value) {
    ElMessage.warning('请先选择模板')
    return
  }

  // 验证配置
  const errors: string[] = []
  buttonGroups.value.forEach((group, index) => {
    const groupErrors = validateButtonGroup(group)
    if (groupErrors.length > 0) {
      errors.push(`按钮组 ${index + 1}: ${groupErrors.join(', ')}`)
    }
  })

  if (errors.length > 0) {
    ElMessage.error(`配置验证失败: ${errors.join('; ')}`)
    return
  }

  try {
    await saveTemplateButtonGroups(selectedTemplateId.value, buttonGroups.value)
    ElMessage.success('配置已保存')
  } catch (error: any) {
    ElMessage.error(error.message || '保存配置失败')
  }
}
</script>

<style scoped>
.status-button-config {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
}

.page-header {
  background: #fff;
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-content {
  padding: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
}

.header-text h1 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.page-content {
  .template-selector {
    margin-bottom: 24px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .groups-list {
    .group-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      margin-bottom: 16px;
      overflow: hidden;
    }

    .group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background: #fafbfc;
      border-bottom: 1px solid #e4e7ed;
    }

    .group-info h3 {
      margin: 0 0 4px 0;
      color: #303133;
    }

    .group-info p {
      margin: 0 0 8px 0;
      color: #606266;
      font-size: 14px;
    }

    .buttons-list {
      padding: 16px;
    }

    .button-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 6px;
      margin-bottom: 8px;
      background: #fff;
    }

    .button-info {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
    }

    .drag-handle {
      cursor: move;
      color: #909399;
    }

    .button-meta {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-left: 12px;
    }

    .api-endpoint {
      font-size: 12px;
      color: #909399;
      font-family: monospace;
    }
  }
}
</style>
