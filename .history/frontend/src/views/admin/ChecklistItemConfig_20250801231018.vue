<template>
  <div class="checklist-item-config">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <div class="header-icon">⚙️</div>
          <div class="header-text">
            <h1>CheckList评审项配置</h1>
            <p>配置检查项的启用状态、分类标签和详细属性</p>
          </div>
        </div>
        <div class="header-actions">
          <div class="quick-stats">
            <div class="stat-badge enabled">
              <span class="stat-dot"></span>
              <span>启用项目</span>
            </div>
            <div class="stat-badge disabled">
              <span class="stat-dot"></span>
              <span>禁用项目</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="page-content">
      <ItemConfig />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import ItemConfig from '@/components/admin/ItemConfig.vue'

// Set page title
onMounted(() => {
  document.title = 'CheckList评审项配置 - Checklist Review System'
})
</script>

<style scoped>
.checklist-item-config {
  min-height: 100vh;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  padding: 24px;
}

.page-header {
  background: #fff;
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-content {
  padding: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
}

.header-text h1 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-text p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.header-actions {
  display: flex;
  gap: 16px;
}

.quick-stats {
  display: flex;
  gap: 12px;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.stat-badge.enabled {
  background: #f0f9ff;
  color: #0369a1;
  border: 1px solid #bae6fd;
}

.stat-badge.disabled {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.stat-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.stat-badge.enabled .stat-dot {
  background: #0369a1;
}

.stat-badge.disabled .stat-dot {
  background: #dc2626;
}

.page-content {
  background: #fff;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
</style>
