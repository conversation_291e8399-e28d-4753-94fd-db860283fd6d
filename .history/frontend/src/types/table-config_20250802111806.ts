import { type ReviewItem, type ReviewItemStatus } from '@/api/review'

// 列配置类型
export interface ColumnConfig {
  id: string
  key: string  // 对应数据字段
  label: string  // 显示名称
  width?: number
  minWidth?: number
  sortable?: boolean
  visible: boolean
  type: 'text' | 'tag' | 'date' | 'number' | 'status' | 'custom'
  fixed?: boolean  // 基础列不可删除
  customRender?: string  // 自定义渲染逻辑
  editable?: boolean  // 是否可编辑
  options?: string[]  // 下拉选项（用于tag类型）
}

// 表格视图配置
export interface TableViewConfig {
  id: string
  name: string
  columns: ColumnConfig[]
  groupBy?: string  // 分组字段
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  showGroupSummary?: boolean  // 是否显示分组统计
  defaultCollapsed?: boolean  // 默认是否折叠分组
}

// 扩展的评审项，支持自定义字段
export interface ExtendedReviewItem extends ReviewItem {
  customFields?: Record<string, any>  // 用户自定义字段
}

// 分组数据结构
export interface GroupedItems {
  [groupKey: string]: {
    items: ExtendedReviewItem[]
    count: number
    collapsed: boolean
    summary?: GroupSummary
  }
}

// 分组统计信息
export interface GroupSummary {
  total: number
  passed: number
  failed: number
  skipped: number
  pending: number
}

// 视图模式
export enum ViewMode {
  CARD = 'card',
  TABLE = 'table'
}

// 数据导出配置
export interface ExportConfig {
  selectedOnly?: boolean  // 仅导出选中项
  includeCustomFields?: boolean  // 包含自定义字段
  format: 'json' | 'csv' | 'excel'
  columns?: string[]  // 指定导出的列
}

// 自定义字段定义
export interface CustomFieldDefinition {
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'select' | 'boolean'
  required?: boolean
  defaultValue?: any
  options?: string[]  // 用于select类型
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

// 表格配置管理
export interface TableConfigManager {
  configs: TableViewConfig[]
  activeConfigId: string
  customFields: CustomFieldDefinition[]
}

// 默认列配置
export const DEFAULT_COLUMNS: ColumnConfig[] = [
  {
    id: 'selection',
    key: 'selection',
    label: '选择',
    width: 60,
    type: 'custom',
    visible: true,
    fixed: true,
    sortable: false
  },
  {
    id: 'sequence',
    key: 'sequence',
    label: '序号',
    width: 80,
    type: 'number',
    visible: true,
    fixed: true,
    sortable: true
  },
  {
    id: 'content',
    key: 'content',
    label: '检查内容',
    minWidth: 300,
    type: 'text',
    visible: true,
    fixed: true,
    sortable: false
  },
  {
    id: 'status',
    key: 'status',
    label: '状态',
    width: 120,
    type: 'status',
    visible: true,
    fixed: true,
    sortable: true
  },
  {
    id: 'category',
    key: 'category',
    label: '分类',
    width: 120,
    type: 'tag',
    visible: true,
    sortable: true
  },
  {
    id: 'reviewer',
    key: 'reviewer',
    label: '评审人',
    width: 120,
    type: 'text',
    visible: true,
    sortable: true
  },
  {
    id: 'reviewTime',
    key: 'reviewTime',
    label: '评审时间',
    width: 160,
    type: 'date',
    visible: true,
    sortable: true
  },
  {
    id: 'comment',
    key: 'comment',
    label: '备注',
    minWidth: 200,
    type: 'text',
    visible: false,
    sortable: false
  }
]

// 默认表格配置
export const DEFAULT_TABLE_CONFIG: TableViewConfig = {
  id: 'default',
  name: '默认视图',
  columns: DEFAULT_COLUMNS,
  groupBy: 'category',
  sortBy: 'sequence',
  sortOrder: 'asc',
  showGroupSummary: true,
  defaultCollapsed: false
}

// 工具函数：创建新的自定义列
export function createCustomColumn(field: CustomFieldDefinition): ColumnConfig {
  return {
    id: `custom_${field.key}`,
    key: field.key,
    label: field.label,
    width: 150,
    type: field.type === 'select' ? 'tag' : 'text',
    visible: true,
    fixed: false,
    sortable: true,
    editable: true,
    options: field.options
  }
}

// 工具函数：获取分组统计
export function calculateGroupSummary(items: ExtendedReviewItem[]): GroupSummary {
  return {
    total: items.length,
    passed: items.filter(item => item.status === ReviewItemStatus.PASS).length,
    failed: items.filter(item => item.status === ReviewItemStatus.FAIL).length,
    skipped: items.filter(item => item.status === ReviewItemStatus.SKIP).length,
    pending: items.filter(item => item.status === ReviewItemStatus.PENDING).length
  }
}

// 工具函数：按字段分组
export function groupItemsByField(items: ExtendedReviewItem[], field: string): GroupedItems {
  const groups: GroupedItems = {}
  
  items.forEach(item => {
    const groupKey = getGroupKey(item, field)
    if (!groups[groupKey]) {
      groups[groupKey] = {
        items: [],
        count: 0,
        collapsed: false
      }
    }
    groups[groupKey].items.push(item)
    groups[groupKey].count++
  })
  
  // 计算每组的统计信息
  Object.keys(groups).forEach(key => {
    groups[key].summary = calculateGroupSummary(groups[key].items)
  })
  
  return groups
}

// 工具函数：获取分组键值
function getGroupKey(item: ExtendedReviewItem, field: string): string {
  if (field.startsWith('customFields.')) {
    const customField = field.replace('customFields.', '')
    return item.customFields?.[customField] || '未分类'
  }
  
  const value = (item as any)[field]
  return value || '未分类'
}
