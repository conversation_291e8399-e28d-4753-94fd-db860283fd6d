import { describe, it, expect } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import router from '@/router'

describe('Router', () => {
  it('should have correct routes configured', () => {
    const routes = router.getRoutes()
    
    // Check that main routes exist
    const routeNames = routes.map(route => route.name).filter(Boolean)
    
    expect(routeNames).toContain('ChecklistContentEditor')
    expect(routeNames).toContain('ChecklistItemConfig')
    expect(routeNames).toContain('ChecklistSettings')
    expect(routeNames).toContain('ReviewDashboard')
    expect(routeNames).toContain('ChecklistReview')
    expect(routeNames).toContain('ReviewHistory')
  })

  it('should redirect root path to review dashboard', () => {
    const routes = router.getRoutes()
    const rootRoute = routes.find(route => route.path === '/')
    
    expect(rootRoute?.redirect).toBe('/review')
  })

  it('should have correct meta information for routes', () => {
    const routes = router.getRoutes()

    const contentEditorRoute = routes.find(route => route.name === 'ChecklistContentEditor')
    expect(contentEditorRoute?.meta?.title).toBe('CheckList评审表内容编辑')

    const itemConfigRoute = routes.find(route => route.name === 'ChecklistItemConfig')
    expect(itemConfigRoute?.meta?.title).toBe('CheckList评审项配置')

    const settingsRoute = routes.find(route => route.name === 'ChecklistSettings')
    expect(settingsRoute?.meta?.title).toBe('CheckList配置')

    const reviewRoute = routes.find(route => route.name === 'ReviewDashboard')
    expect(reviewRoute?.meta?.title).toBe('评审系统')
    expect(reviewRoute?.meta?.breadcrumb).toBeDefined()
  })

  it('should handle 404 routes', () => {
    const routes = router.getRoutes()
    const notFoundRoute = routes.find(route => route.name === 'NotFound')
    
    expect(notFoundRoute).toBeDefined()
    expect(notFoundRoute?.redirect).toBe('/review')
  })
})