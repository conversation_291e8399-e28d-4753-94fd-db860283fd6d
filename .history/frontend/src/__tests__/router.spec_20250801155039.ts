import { describe, it, expect } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import router from '@/router'

describe('Router', () => {
  it('should have correct routes configured', () => {
    const routes = router.getRoutes()
    
    // Check that main routes exist
    const routeNames = routes.map(route => route.name).filter(Boolean)
    
    expect(routeNames).toContain('AdminDashboard')
    expect(routeNames).toContain('ReviewDashboard')
    expect(routeNames).toContain('ChecklistReview')
    expect(routeNames).toContain('ReviewHistory')
  })

  it('should redirect root path to review dashboard', () => {
    const routes = router.getRoutes()
    const rootRoute = routes.find(route => route.path === '/')
    
    expect(rootRoute?.redirect).toBe('/review')
  })

  it('should have correct meta information for routes', () => {
    const routes = router.getRoutes()
    
    const adminRoute = routes.find(route => route.name === 'AdminDashboard')
    expect(adminRoute?.meta?.title).toBe('后台管理')
    expect(adminRoute?.meta?.breadcrumb).toBeDefined()
    
    const reviewRoute = routes.find(route => route.name === 'ReviewDashboard')
    expect(reviewRoute?.meta?.title).toBe('评审系统')
    expect(reviewRoute?.meta?.breadcrumb).toBeDefined()
  })

  it('should handle 404 routes', () => {
    const routes = router.getRoutes()
    const notFoundRoute = routes.find(route => route.name === 'NotFound')
    
    expect(notFoundRoute).toBeDefined()
    expect(notFoundRoute?.redirect).toBe('/review')
  })
})