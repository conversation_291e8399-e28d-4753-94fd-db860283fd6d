package com.checklist.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 提供系统健康状态检查的 REST API 接口
 */
@RestController
public class HealthController {

    /**
     * 健康检查端点
     * GET /api/health
     *
     * @return 系统健康状态信息
     */
    @GetMapping("/api/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> healthInfo = new HashMap<>();
        
        try {
            // 基本健康状态
            healthInfo.put("status", "UP");
            healthInfo.put("message", "系统运行正常");
            healthInfo.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            healthInfo.put("service", "Checklist Review System");
            healthInfo.put("version", "1.0.0");
            
            // 系统信息
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("osName", System.getProperty("os.name"));
            systemInfo.put("osVersion", System.getProperty("os.version"));
            systemInfo.put("availableProcessors", Runtime.getRuntime().availableProcessors());
            
            // 内存信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> memoryInfo = new HashMap<>();
            memoryInfo.put("totalMemory", runtime.totalMemory());
            memoryInfo.put("freeMemory", runtime.freeMemory());
            memoryInfo.put("maxMemory", runtime.maxMemory());
            memoryInfo.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
            
            systemInfo.put("memory", memoryInfo);
            healthInfo.put("system", systemInfo);
            
            // 组件状态检查
            Map<String, Object> components = new HashMap<>();
            
            // 检查数据目录
            components.put("dataDirectory", checkDataDirectory());
            
            // 检查文件系统
            components.put("fileSystem", checkFileSystem());
            
            healthInfo.put("components", components);
            
            return ResponseEntity.ok(healthInfo);
            
        } catch (Exception e) {
            // 如果健康检查过程中出现异常，返回DOWN状态
            healthInfo.clear();
            healthInfo.put("status", "DOWN");
            healthInfo.put("message", "系统检查时发生错误: " + e.getMessage());
            healthInfo.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            healthInfo.put("service", "Checklist Review System");
            
            return ResponseEntity.status(503).body(healthInfo);
        }
    }
    
    /**
     * 简化的健康检查端点（兼容性）
     * GET /health
     *
     * @return 简化的健康状态信息
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> simpleHealth() {
        Map<String, Object> healthInfo = new HashMap<>();
        healthInfo.put("status", "UP");
        healthInfo.put("message", "系统运行正常");
        healthInfo.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        healthInfo.put("service", "Checklist Review System");

        return ResponseEntity.ok(healthInfo);
    }
    
    /**
     * 检查数据目录状态
     * 
     * @return 数据目录状态信息
     */
    private Map<String, Object> checkDataDirectory() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            java.nio.file.Path dataPath = java.nio.file.Paths.get("data");
            java.nio.file.Path templatesPath = java.nio.file.Paths.get("data/templates");
            java.nio.file.Path reviewsPath = java.nio.file.Paths.get("data/reviews");
            
            status.put("status", "UP");
            status.put("dataDirectoryExists", java.nio.file.Files.exists(dataPath));
            status.put("templatesDirectoryExists", java.nio.file.Files.exists(templatesPath));
            status.put("reviewsDirectoryExists", java.nio.file.Files.exists(reviewsPath));
            
            if (java.nio.file.Files.exists(dataPath)) {
                status.put("dataDirectoryWritable", java.nio.file.Files.isWritable(dataPath));
            }
            
        } catch (Exception e) {
            status.put("status", "DOWN");
            status.put("error", e.getMessage());
        }
        
        return status;
    }
    
    /**
     * 检查文件系统状态
     * 
     * @return 文件系统状态信息
     */
    private Map<String, Object> checkFileSystem() {
        Map<String, Object> status = new HashMap<>();
        
        try {
            java.io.File currentDir = new java.io.File(".");
            
            status.put("status", "UP");
            status.put("currentDirectory", currentDir.getAbsolutePath());
            status.put("canRead", currentDir.canRead());
            status.put("canWrite", currentDir.canWrite());
            status.put("freeSpace", currentDir.getFreeSpace());
            status.put("totalSpace", currentDir.getTotalSpace());
            status.put("usableSpace", currentDir.getUsableSpace());
            
        } catch (Exception e) {
            status.put("status", "DOWN");
            status.put("error", e.getMessage());
        }
        
        return status;
    }
}
